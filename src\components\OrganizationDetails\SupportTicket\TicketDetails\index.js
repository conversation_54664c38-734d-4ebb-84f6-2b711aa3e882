'use client';
import { Box, Typography } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import React, { useState } from 'react';
import { DateFormat } from '@/helper/common/commonFunctions';
import CustomTabs from '@/components/UI/CustomTabs';
import Conversation from './Conversation';
// TODO: Uncomment TimeEntry import when needed in future
// import TimeEntry from './TimeEntry';
import Attachment from './Attachment';
import TicketInformation from './TicketInformation';
import History from './History';
import './ticketdetails.scss';

export default function TicketDetails({ ticket }) {
  const [activeTab, setActiveTab] = useState(1);

  const reports_tabs = [
    { id: 1, label: 'Ticket Information' },
    { id: 2, label: 'Conversation' },
    // TODO: Uncomment Time Entry when needed in future
    // { id: 3, label: 'Time Entry' },
    { id: 4, label: 'Attachment' },
    { id: 5, label: 'History' },
  ];

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  const getCurrentContent = () => {
    switch (activeTab) {
      case 1:
        return <TicketInformation ticket={ticket} />;
      case 2:
        return <Conversation />;
      // TODO: Uncomment Time Entry when needed in future
      // case 3:
      //   return <TimeEntry />;
      case 4:
        return <Attachment />;
      case 5:
        return <History />;
      default:
        return <TicketInformation ticket={ticket} />;
    }
  };

  return (
    <Box className="details-wrap">
      <Box className="ticket-details-wrap">
        <Box className="d-flex align-center gap-sm mb8">
          <Typography className="id-wrap body-sm">#{ticket?.id}</Typography>
          <Typography className="sub-header-text">{ticket?.subject}</Typography>
        </Box>
        <Box className="detail-wrap d-flex align-center gap-20">
          <Box className="d-flex align-center gap-sm user-info-section">
            <PersonIcon className="user-icon" />
            <Box className="d-flex align-center">
              <Typography className="body-sm pr4">
                {ticket?.userName}
              </Typography>
              <Typography className="from-text body-sm pr4">from</Typography>
              <Typography className="body-sm">
                {ticket?.organizationName}
              </Typography>
            </Box>
          </Box>

          <Box className="d-flex align-center gap-sm time-info-section">
            <AccessTimeIcon className="time-icon" />
            <Typography className="date-time-text body-sm">
              {ticket?.createdAt &&
                DateFormat(ticket.createdAt, 'datesWithhour')}
            </Typography>
          </Box>
          {/* #TODO: Currently not in use if needed then uncomment */}
          {/* <Box className="d-flex align-center">
            <QueryBuilderIcon className="timer-icon" />
            <Typography className="time-wrap body-sm">Time</Typography>
          </Box>
          <Box className="watch-wrap">
            <Stopwatch
              time={time}
              setTime={setTime}
              running={running}
              setRunning={setRunning}
              savedTimes={savedTimes}
              setSavedTimes={setSavedTimes}
              showButtons={showButtons}
              setShowButtons={setShowButtons}
            />
          </Box> */}
        </Box>
      </Box>
      <Box className="ticket-tab-handler">
        <CustomTabs
          tabs={reports_tabs}
          initialTab={activeTab}
          onTabChange={handleTabChange}
        />
        <Box className="pt24">{getCurrentContent()}</Box>
      </Box>
    </Box>
  );
}
