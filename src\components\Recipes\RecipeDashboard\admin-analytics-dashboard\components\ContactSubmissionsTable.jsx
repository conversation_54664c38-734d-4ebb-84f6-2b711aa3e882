'use client';
import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  gridClasses,
  useTheme,
  useMediaQuery,
  Popover,
} from '@mui/material';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomSelect from '@/components/UI/CustomSelect';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import CustomOrgPagination from '@/components/UI/customPagination';
import FilterListIcon from '@mui/icons-material/FilterList';
// import DownloadIcon from '@mui/icons-material/Download';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import {
  getPublicContactAnalytics,
  deleteContactSubmission,
  exportContactSubmissions,
} from '@/services/recipeService';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { staticOptions } from '@/helper/common/staticOptions';

// Helper function to format date using common function
const formatDate = (dateString) => {
  if (!dateString) return '-';
  return DateFormat(dateString, 'datesWithhour');
};

const createSortableHeader = (field, label, sortOrder, onSort) => (
  <Box className="d-flex align-center gap-5">
    <Box className="wrap-header-text d-flex align-center">
      <Typography className="title-text fw600">{label}</Typography>
      <Box className="amount-text arrow-wrap">
        {sortOrder?.key === field && sortOrder?.value === 'DESC' ? (
          <ArrowDownward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        ) : (
          <ArrowUpward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        )}
      </Box>
    </Box>
  </Box>
);

// Contact Submissions Columns
const createContactColumns = (
  sortOrder,
  handleSort,
  currentPage,
  rowsPerPage,
  paginatedData,
  handleDelete
) => [
  {
    field: 'id',
    headerName: 'ID',
    width: 48,
    minWidth: 48,
    sortable: false,
    flex: 0,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      const rowIndex = paginatedData?.findIndex(
        (row) => row?.id === params?.row?.id
      );
      const sequentialNumber = (currentPage - 1) * rowsPerPage + rowIndex + 1;
      return (
        <Typography className="text-ellipsis">{sequentialNumber}</Typography>
      );
    },
  },
  {
    field: 'recipeName',
    headerName: 'Recipe Name',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader(
        'recipe_title',
        'Recipe Name',
        sortOrder,
        handleSort
      ),
    renderCell: (params) => {
      const recipeNameValue =
        params?.row?.recipeName &&
        params?.row?.recipeName !== '-' &&
        params?.row?.recipeName.trim() !== ''
          ? params?.row?.recipeName
          : '-';

      const hasValidData = recipeNameValue !== '-';

      return (
        <Box className="text-ellipsis-line">
          {hasValidData ? (
            <Tooltip
              classes={{ tooltip: 'info-tooltip-container' }}
              title={
                <Typography className="sub-title-text">
                  {recipeNameValue}
                </Typography>
              }
              arrow
            >
              <Typography
                className="title-text text-ellipsis-line"
                title={recipeNameValue}
              >
                {recipeNameValue}
              </Typography>
            </Tooltip>
          ) : (
            <Typography className="title-text text-ellipsis-line">
              {recipeNameValue}
            </Typography>
          )}
        </Box>
      );
    },
  },
  {
    field: 'name',
    headerName: 'Name',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      <Box className="text-ellipsis-line">
        <Typography
          className="title-text text-ellipsis-line"
          title={params?.row?.name}
        >
          {params?.row?.name ?? '-'}
        </Typography>
      </Box>
    ),
  },
  {
    field: 'email',
    headerName: 'Email',
    width: 200,
    minWidth: 200,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      <Box className="text-ellipsis-line">
        <Typography
          className="title-text text-ellipsis-line"
          title={params?.row?.email}
        >
          {params?.row?.email ?? '-'}
        </Typography>
      </Box>
    ),
  },
  {
    field: 'mobile',
    headerName: 'Mobile',
    width: 120,
    minWidth: 120,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      <Box className="text-ellipsis-line">
        <Typography
          className="title-text text-ellipsis-line"
          title={params?.row?.mobile}
        >
          {params?.row?.mobile ?? '-'}
        </Typography>
      </Box>
    ),
  },
  {
    field: 'message',
    headerName: 'Message',
    width: 200,
    minWidth: 200,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => (
      <Tooltip
        title={
          <Typography className="sub-title-text">
            {params?.row?.message}
          </Typography>
        }
        arrow
        classes={{ tooltip: 'info-tooltip-container' }}
      >
        <Typography className="title-text text-ellipsis">
          {params?.row?.message?.length > 30
            ? `${params?.row?.message?.substring(0, 30)}...`
            : params?.row?.message}
        </Typography>
      </Tooltip>
    ),
  },
  {
    field: 'submittedOn',
    headerName: 'Submitted On',
    width: 150,
    minWidth: 150,
    flex: 1,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderHeader: () =>
      createSortableHeader('created_at', 'Submitted On', sortOrder, handleSort),
    renderCell: (params) => (
      <Typography className="title-text">
        {params?.row?.submittedOn ?? '-'}
      </Typography>
    ),
  },
  {
    field: 'actions',
    headerName: 'Actions',
    width: 80,
    minWidth: 80,
    flex: 0,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderCell: (params) => (
      <Box className="d-flex actions align-center justify-center h100">
        <Tooltip
          title={<Typography className="sub-title-text">Delete</Typography>}
          arrow
          classes={{ tooltip: 'info-tooltip-container' }}
        >
          <Box className="d-flex">
            <DeleteIcon onClick={() => handleDelete(params?.row?.id)} />
          </Box>
        </Tooltip>
      </Box>
    ),
  },
];

const ContactSubmissionsTable = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  // Contact Submissions State
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [sortOrder, setSortOrder] = useState({
    key: '',
    value: 'ASC',
  });
  const [filterData, setFilterData] = useState({
    date_range: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    date_range: '',
    searchValue: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  // API Data State
  const [contactData, setContactData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);

  // Filter drawer state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);

  // Delete modal state
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);

  // Export popover state
  const [exportAnchorEl, setExportAnchorEl] = useState(null);
  const exportOpen = Boolean(exportAnchorEl);
  const exportId = exportOpen ? 'export-popover' : undefined;

  // Filter options (following RecipeCTAAnalyticsTable pattern)
  const filters = [
    {
      key: 'search',
      label: 'Search Recipe Name',
      options: [],
      permission: true,
    },
    {
      key: 'date_range',
      label: 'Date Range',
      permission: true,
      options: staticOptions?.ANALYTICS_DATE_RANGES,
    },
  ];

  // Filter management functions (following RecipeCTAAnalyticsTable pattern)
  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      const safeFilters = prevFilters || [];
      if (safeFilters?.includes(key)) {
        return safeFilters?.filter((item) => item !== key);
      } else {
        const index = filters?.findIndex((filter) => filter?.key === key);
        const newFilters = [...safeFilters];
        if (index >= 0) {
          newFilters?.splice(index, 0, key);
        } else {
          newFilters?.push(key);
        }
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    // const safeFilters = selectedFilters || [];
    // const firstFour = safeFilters?.slice(0, 4);
    // setSelectedFilters(firstFour);
    // saveToStorage(identifiers?.CONTACT_SUBMISSIONS_FILTER, firstFour);
    // const savedFilters = fetchFromStorage(
    //   identifiers?.CONTACT_SUBMISSIONS_FILTER
    // );
    // setSelectedFilters(savedFilters?.slice(0, 4));
    // saveToStorage(
    //   identifiers?.CONTACT_SUBMISSIONS_FILTER,
    //   savedFilters?.slice(0, 4)
    // );

    setSelectedFilters(
      filters
        ?.map((item) => {
          return item?.key;
        })
        ?.slice(0, 4)
    );
    saveToStorage(
      identifiers?.CONTACT_SUBMISSIONS_FILTER,
      filters
        ?.map((item) => {
          return item?.key;
        })
        ?.slice(0, 4)
    );
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.CONTACT_SUBMISSIONS_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  // Initialize selected filters
  useEffect(() => {
    const savedFilters = fetchFromStorage(
      identifiers?.CONTACT_SUBMISSIONS_FILTER
    );
    if (!savedFilters) {
      const defaultFilters =
        filters?.slice(0, 4)?.map((filter) => filter?.key) || [];
      setSelectedFilters(defaultFilters);
    } else {
      setSelectedFilters(savedFilters);
    }
  }, []);

  // Wrapper function for API calls (following RecipeCTAAnalyticsTable pattern)
  const getContactAnalyticsData = async (search, page, filter, Rpp, Sort) => {
    try {
      setIsLoading(true);

      // Create filter object with search as recipe_name (following RecipeCTAAnalyticsTable pattern)
      const apiFilter = {
        ...(search && { recipe_name: search }), // Pass search value as recipe_name
        ...(filter?.date_range && { date_range: filter.date_range }),
      };

      const { data, totalRecords } = await getPublicContactAnalytics(
        page,
        apiFilter,
        Rpp,
        Sort
      );

      // Transform API data to match table structure
      const transformedData =
        data?.map((item, index) => ({
          id: item?.id || item?.submission_id || index + 1,
          recipeName: item?.recipe_name || `Recipe ${item?.recipe_id}`,
          name: item?.name || '-',
          email: item?.email || '-',
          mobile: item?.mobile || '-',
          message: item?.message || '-',
          submittedOn: formatDate(item?.submitted_on),
        })) || [];

      setContactData(transformedData);
      setTotalCount(totalRecords || transformedData?.length);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message ||
          'Failed to fetch contact submissions data'
      );
      // Clear data on error since we're using API-only approach
      setContactData([]);
      setTotalCount(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle sorting (following RecipeCTAAnalyticsTable pattern)
  const handleSort = async (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setCurrentPage(1);

    // Call API with new sort order
    await getContactAnalyticsData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      rowsPerPage,
      newSortOrder
    );
  };

  // Handle search (following RecipeCTAAnalyticsTable pattern)
  const handleSearch = async () => {
    try {
      // Call API with current search and filter values
      await getContactAnalyticsData(
        searchValue,
        1, // Reset to first page
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
      // Apply current search value to filterDataApplied
      setFilterDataApplied((prev) => ({
        ...prev,
        searchValue: searchValue,
      }));
      setCurrentPage(1);
    } finally {
      // Handle any cleanup if needed
    }
  };

  // Handle key press (following IngredientList pattern)
  const handleKeyPress = async (event) => {
    if (event?.key === 'Enter') {
      await handleSearch();
    }
  };

  const handleApplyFilters = async () => {
    try {
      setCurrentPage(1);
      const newFilterData = {
        date_range: filterData?.date_range,
        searchValue: searchValue,
      };
      setFilterDataApplied(newFilterData);

      // Call API with updated filter data
      await getContactAnalyticsData(
        searchValue,
        1,
        newFilterData,
        rowsPerPage,
        sortOrder
      );

      // Close drawer after applying filters
      setOpenFilterDrawer(false);
    } finally {
      // Handle any cleanup if needed
    }
  };

  // Handle clear filters (following RecipeCTAAnalyticsTable pattern)
  const handleClearFilters = async () => {
    setSearchValue('');
    setFilterData({
      date_range: '',
    });
    setFilterDataApplied({
      date_range: '',
      searchValue: '',
    });
    setCurrentPage(1);

    // Call API with cleared filters
    try {
      await getContactAnalyticsData(
        '',
        1,
        {
          date_range: '',
          searchValue: '',
        },
        rowsPerPage,
        sortOrder
      );

      // Close drawer after clearing filters
      setOpenFilterDrawer(false);
    } finally {
      // Handle any cleanup if needed
    }
  };

  // Handle delete
  const handleDelete = (id) => {
    setDeleteDialogOpen(true);
    setDeleteId(id);
  };

  const handleConfirmDelete = async () => {
    try {
      // Call the delete API
      await deleteContactSubmission(deleteId);

      // Show success message
      setApiMessage('success', 'Contact submission deleted successfully');

      // Refresh the data after deletion
      await getContactAnalyticsData(
        filterDataApplied.searchValue || '',
        currentPage,
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to delete contact submission'
      );
    }
    handleCloseDeleteDialog();
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  // Export handlers
  // const handleExportClick = (event) => {
  //   setExportAnchorEl(event.currentTarget);
  // };

  const handleExportClose = () => {
    setExportAnchorEl(null);
  };

  const handleExportDownload = async (format) => {
    try {
      // Prepare export filters
      const exportFilters = {
        search: filterDataApplied.searchValue || searchValue,
        recipe_name: filterDataApplied.searchValue || searchValue,
        date_range: filterDataApplied.date_range,
      };

      // Call the export API
      const response = await exportContactSubmissions(format, exportFilters);

      // Create blob and trigger download from API response
      const blob = response?.data;
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Determine file extension based on format
      const fileExtension = format === 'excel' ? 'xlsx' : 'csv';

      link.setAttribute(
        'download',
        `contact_submissions_${new Date().toISOString()?.split('T')?.[0]}.${fileExtension}`
      );
      document.body.appendChild(link);
      link.click();
      link?.parentNode?.removeChild(link);
      window.URL.revokeObjectURL(url);

      setApiMessage('success', 'Export completed successfully');
      handleExportClose();
    } catch (error) {
      console.error('Export failed:', error);
      setApiMessage('error', 'Export failed. Please try again.');
      handleExportClose();
    }
  };

  // Pagination handlers (following RecipeCTAAnalyticsTable pattern)
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    await getContactAnalyticsData(
      filterDataApplied.searchValue || '',
      newPage,
      filterDataApplied,
      rowsPerPage,
      sortOrder
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    await getContactAnalyticsData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      newRowsPerPage,
      sortOrder
    );
  };

  // Load initial data on component mount (following RecipeCTAAnalyticsTable pattern)
  useEffect(() => {
    getContactAnalyticsData('', 1, filterDataApplied, rowsPerPage, sortOrder);
  }, []);

  // Contact Submissions columns
  const columns = createContactColumns(
    sortOrder,
    handleSort,
    currentPage,
    rowsPerPage,
    contactData,
    handleDelete
  );

  return (
    <Box className="">
      {/* <Box className="d-flex justify-space-between align-center pb4">
      <Typography className="sub-header-text fw600">
          Contact Form Submissions
        </Typography>
      <CustomButton
          variant="outlined"
          isIconOnly
          startIcon={
            <Tooltip
              title={<Typography className="sub-title-text">Export</Typography>}
              arrow
              classes={{ tooltip: 'info-tooltip-container' }}
            >
              <DownloadIcon />
            </Tooltip>
          }
          onClick={handleExportClick}
        />
      </Box> */}
      {/* Contact Filters (following RecipeCTAAnalyticsTable pattern) */}
      <Box className="search-section-wrap">
        {!isMobile &&
          selectedFilters?.map((key) => {
            const filter = filters?.find((f) => f?.key === key);
            return filter?.permission ? (
              <React.Fragment key={key}>
                {key === 'search' ? (
                  <Box className="search-section-fields">
                    <CustomSearch
                      fullWidth
                      placeholder="Search by recipe name..."
                      setSearchValue={setSearchValue}
                      onKeyPress={handleKeyPress}
                      searchValue={searchValue}
                    />
                  </Box>
                ) : (
                  <Box className="search-section-fields">
                    <CustomSelect
                      placeholder={filter?.label}
                      options={filter?.options}
                      value={
                        filter?.options?.find((opt) => {
                          return opt?.value === filterData?.[key];
                        }) || ''
                      }
                      onChange={(e) =>
                        setFilterData({
                          ...filterData,
                          [key]: e?.value,
                        })
                      }
                      menuPortalTarget={document.body}
                      styles={{
                        menuPortal: (base) => ({
                          ...base,
                          zIndex: 9999,
                        }),
                      }}
                    />
                  </Box>
                )}
              </React.Fragment>
            ) : null;
          })}

        {!isMobile && (
          <>
            <Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Apply Filter
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <CheckIcon />
                  </Tooltip>
                }
                onClick={handleApplyFilters}
              />
            </Box>
            <Box>
              <CustomButton
                variant="outlined"
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Clear Filter
                      </Typography>
                    }
                    arrow
                    classes={{
                      tooltip: 'info-tooltip-container',
                    }}
                  >
                    <ClearOutlinedIcon />
                  </Tooltip>
                }
                onClick={handleClearFilters}
              />
            </Box>
          </>
        )}
        <Box>
          <CustomButton
            isIconOnly
            startIcon={
              <Tooltip
                title={
                  <Typography className="sub-title-text">Filter</Typography>
                }
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
                arrow
              >
                <FilterListIcon />
              </Tooltip>
            }
            onClick={() => {
              setOpenFilterDrawer(true);
            }}
          />
        </Box>
      </Box>

      {/* Contact Table */}
      <Box className="table-container table-layout">
        {isLoading ? (
          <ContentLoader />
        ) : contactData?.length === 0 ? (
          <NoDataView
            title="No Contact Submissions found"
            description="There are no contact form submissions available at the moment."
            className="no-data-auto-height-conainer"
          />
        ) : (
          <>
            <DataGrid
              key={`datagrid-contact-${contactData?.length}-${currentPage}`}
              rows={contactData}
              columns={columns}
              pageSize={rowsPerPage}
              rowCount={totalCount}
              checkboxSelection={false}
              disableSelectionOnClick
              hideMenuIcon
              paginationMode="server"
              disableVirtualization={false}
              sx={{
                transition: 'none !important',
                animation: 'none !important',
                '& *': {
                  transition: 'none !important',
                  animation: 'none !important',
                  transform: 'none !important',
                },
                [`& .${gridClasses.cell}`]: {
                  py: 1,
                  transition: 'none',
                },
              }}
            />
            <CustomOrgPagination
              currentPage={currentPage}
              totalCount={totalCount}
              rowsPerPage={rowsPerPage}
              onPageChange={handlePageChange}
              OnRowPerPage={handleRowsPerPageChange}
            />
          </>
        )}
      </Box>

      {/* Filter Drawer (following RecipeCTAAnalyticsTable pattern) */}
      <RightDrawer
        anchor={'right'}
        open={openFilterDrawer}
        onClose={() => setOpenFilterDrawer(false)}
        title="Filter"
        className="filter-options-drawer"
        content={
          <FilterComponent
            filters={filters}
            filterData={filterData}
            setFilterData={setFilterData}
            selectedFilters={selectedFilters}
            toggleFilter={toggleFilter}
            saveLayout={saveLayout}
            setOpenFilterDrawer={setOpenFilterDrawer}
            setSelectedFilters={setSelectedFilters}
            getFirstFourFilters={getFirstFourFilters}
            setSearchValue={setSearchValue}
            searchValue={searchValue}
            handleKeyPress={handleKeyPress}
            isMobile={isMobile}
            handleApplyFilter={handleApplyFilters}
            handleClearFilter={handleClearFilters}
          />
        }
      />

      {/* Delete Confirmation Dialog */}
      <DialogBox
        open={deleteDialogOpen}
        handleClose={handleCloseDeleteDialog}
        title="Confirmation"
        className="delete-modal"
        dividerClass="delete-modal-divider"
        content={
          <DeleteModal
            handleCancel={handleCloseDeleteDialog}
            handleConfirm={handleConfirmDelete}
            text="Are you sure you want to delete this contact submission? This action cannot be undone."
          />
        }
      />

      {/* Export Popover */}
      <Popover
        className="export-popover"
        id={exportId}
        open={exportOpen}
        anchorEl={exportAnchorEl}
        onClose={handleExportClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
      >
        <Box className="export-option">
          <Typography
            className="title-text pb8 cursor-pointer"
            onClick={() => handleExportDownload('excel')}
          >
            Excel
          </Typography>
          <Typography
            className="title-text cursor-pointer"
            onClick={() => handleExportDownload('csv')}
          >
            CSV
          </Typography>
        </Box>
      </Popover>
    </Box>
  );
};

export default ContactSubmissionsTable;
