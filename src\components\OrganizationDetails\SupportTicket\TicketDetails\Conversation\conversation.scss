

.convesation-wrap {
  .conversation-text {
    font-family: var(--font-family-primary);
  }
}

.conversation-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 600px;

  .conversation-messages {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-md) var(--spacing-none);

    .message-item {
      margin-bottom: var(--spacing-lg);
      padding: var(--spacing-md);
      border-radius: var(--border-radius-md);
      background-color: var(--color-white);
      border: 1px solid var(--border-color-light-gray);

      &.internal-note {
        background-color: #fff9c4;
        border-color: #f59e0b;
      }

      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        .sender-name {
          font-weight: var(--font-weight-medium);
          color: var(--color-dark);

          .internal-note-badge {
            margin-left: var(--spacing-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: #f59e0b;
            color: white;
            border-radius: var(--border-radius-xs);
            font-size: var(--font-size-xs);
            font-weight: var(--font-weight-medium);
            text-transform: uppercase;
          }
        }

        .message-timestamp {
          color: var(--color-dark-50);
          font-size: var(--font-size-xs);
        }
      }

      .message-content {
        .message-text {
          color: var(--color-dark);
          line-height: var(--line-height-relaxed);
        }
      }
    }
  }

  .internal-note-checkbox {
    padding: var(--spacing-sm) var(--spacing-none);
    border-top: 1px solid var(--border-color-light-gray);
    background-color: var(--color-white);

    .visibility-icon {
      font-size: var(--font-size-md);
      color: var(--color-dark-50);
      transition: color 0.2s ease;

      &.active {
        color: var(--color-primary);
      }
    }

    .checkbox-label {
      color: var(--color-dark-50);
      margin: 0;
    }
  }

  .conversation-input-wrapper {
    .conversation-input-container {
      width: 100%;
    }
    .conversation-send-button-icon {
      height: 100%;
      min-height: 75px;
      max-height: 75px;
    }
  }
}
