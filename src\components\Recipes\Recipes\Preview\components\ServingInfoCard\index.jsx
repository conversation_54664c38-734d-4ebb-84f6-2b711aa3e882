import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import NoDataView from '@/components/UI/NoDataView';
import { staticOptions } from '@/helper/common/staticOptions';
import './ServingInfoCard.scss';

const ServingInfoCard = ({
  recipeData,
  isPublicPage = false,
  highlightData = null,
}) => {
  // Check if serving info is highlighted
  const isServingHighlighted =
    highlightData && Object.keys(highlightData)?.length > 0;

  // Helper function to render serving text with highlighting
  const renderServingTextWithHighlight = (text, fieldType) => {
    if (isServingHighlighted && highlightData?.[fieldType]) {
      const highlightText = highlightData[fieldType];
      if (highlightText !== text) {
        return (
          <div className="highlight-container">
            <p className="serving-info-card__text highlight-no-margin-bottom">
              {text}
            </p>
            <p className="highlight-original-text">{highlightText}</p>
          </div>
        );
      }
    }

    // Normal display
    return <p className="serving-info-card__text">{text}</p>;
  };

  // Check if there's any serving data to display
  const hasServingMethod = recipeData?.recipe_serving_method;
  const hasServeIn = recipeData?.recipe_serve_in;
  const hasGarnish = recipeData?.recipe_garnish;

  const hasAnyData = hasServingMethod || hasServeIn || hasGarnish;

  return (
    <div className="serving-info-card">
      <div className="serving-info-card__header">
        <p
          className={`serving-info-card__title ${isServingHighlighted ? 'highlight-no-margin-bottom' : ''}`}
        >
          <Icon name="Utensils" size={18} color="currentColor" />
          <span>Serving & Presentation</span>
        </p>
      </div>

      <div className="serving-info-card__content">
        {!hasAnyData ? (
          <NoDataView
            title="No Serving Information"
            description="There is no serving or presentation information available for this recipe."
            className="no-data-auto-height-conainer"
          />
        ) : (
          <div className="serving-info-card__info">
            {recipeData?.recipe_serving_method && (
              <div className="serving-info-card__item">
                <p className="serving-info-card__label">Serving Method:</p>
                {renderServingTextWithHighlight(
                  staticOptions?.SERVING_METHODS_OPTIONS?.find((item) => {
                    return item?.value === recipeData?.recipe_serving_method;
                  })?.label || '',
                  'recipe_serving_method'
                )}
              </div>
            )}

            {/* Show serve in: always for private pages, data-driven for public pages */}
            {(!isPublicPage || (isPublicPage && recipeData?.recipe_serve_in)) &&
              recipeData?.recipe_serve_in && (
                <div className="serving-info-card__item">
                  <p className="serving-info-card__label">Serve In:</p>
                  {renderServingTextWithHighlight(
                    staticOptions?.SERVE_IN_OPTIONS?.find((item) => {
                      return item?.value === recipeData?.recipe_serve_in;
                    })?.label || '',
                    'recipe_serve_in'
                  )}
                </div>
              )}

            {/* Show garnish: always for private pages, data-driven for public pages */}
            {(!isPublicPage || (isPublicPage && recipeData?.recipe_garnish)) &&
              recipeData?.recipe_garnish && (
                <div className="serving-info-card__item">
                  <p className="serving-info-card__label">Garnish:</p>
                  {renderServingTextWithHighlight(
                    recipeData?.recipe_garnish,
                    'recipe_garnish'
                  )}
                </div>
              )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ServingInfoCard;
