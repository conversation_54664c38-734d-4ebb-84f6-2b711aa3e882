export const identifiers = {
  AUTH_DATA: 'authData',
  EMAIL: 'Email',
  USER_ID: 'User_Id',
  USER_DATA: 'UserData',
  RedirectData: 'RedirectData',
  DEVICEID: 'deviceId',
  DEVICEDATA: 'DEVICEDATA',
  USER_ONBOARD: 'USER_ONBOARD',
  USER_ORG_TOKEN: 'userOrgToken',
  NORMAL_LOGIN: 'isNormalLogin',
  LOGIN_ORG: 'isLoginORG',
  PYMENT_PROVIDER: 'stripe',
  INGREDIENT_CAT_FILTER: 'IngredientCatFilter',
  RECIPE_CAT_FILTER: 'RecipeCatFilter',
  INGREDIENT_FILTER: 'IngredientFilter',
  ALLERGEN_FILTER: 'AllergenFilter',
  CTA_ANALYTICS_FILTER: 'CTAAnalyticsFilter',
  CONTACT_SUBMISSIONS_FILTER: 'ContactSubmissionsFilter',
  RECIPE_CREATION_DATA: 'recipeCreationData',
  RECIPE_PUBLIC_ORG_DATA: 'recipePublicOrgData',
  STATUS: [
    { label: 'Active', value: 'active' },
    { label: 'Draft', value: 'draft' },
    { label: 'In-Active', value: 'inactive' },
  ],
  STATUSDELETED: [
    { label: 'Active', value: 'active' },
    { label: 'Draft', value: 'draft' },
    { label: 'In-Active', value: 'inactive' },
    { label: 'Deleted', value: 'delete' },
  ],
  CARD_STATUS: [
    { label: 'Active', value: 'active' },
    { label: 'In-Active', value: 'inactive' },
  ],
  USER_STATUS_OPTIONS: [
    { value: 'pending', label: 'Pending' },
    { value: 'active', label: 'Active' },
    { value: 'ongoing', label: 'Ongoing' },
    { value: 'completed', label: 'Completed' },
    { value: 'verified', label: 'Verified' },
    { value: 'deleted', label: 'Deleted' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'cancelled', label: 'Cancelled' },
  ],
  RESIGNATION_OPTIONS: [
    { label: 'Pending', value: 'pending' },
    { label: 'In-discussion', value: 'in-discussion' },
    { label: 'Accepted', value: 'accepted' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
  ],
  RESIGNATION_OPTION: [
    { label: 'In-discussion', value: 'in-discussion' },
    { label: 'Accepted', value: 'accepted' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
  ],
  CHANGE_REQUEST_OPTION: [
    { label: 'Pending', value: 'pending' },
    { label: 'Reopened', value: 'reopened' },
    { label: 'Approved', value: 'approved' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
    { label: 'Closed', value: 'closed' },
    { label: 'Deleted', value: 'deleted' },
  ],
  CHANGE_REQUEST_UPDATE_OPTION: [
    { label: 'Approved', value: 'approved' },
    { label: 'Rejected', value: 'rejected' },
    { label: 'Cancelled', value: 'cancelled' },
    { label: 'Closed', value: 'closed' },
  ],
  CHANGE_REQUEST_UPDATE_OPTIONS: [
    { label: 'Cancelled', value: 'cancelled' },
    { label: 'Closed', value: 'closed' },
  ],
  TRAINING_FILTER_STATUS: [
    { label: 'Completed', value: 'completed' },
    { label: 'Pending', value: 'pending' },
    { label: 'Ongoing', value: 'ongoing' },
  ],
  DURATION_TYPE: [
    { label: 'Weekly', value: 'week' },
    { label: 'Monthly', value: 'month' },
  ],
  WAGE_TYPE: [
    { label: 'Fixed', value: 'fixed' },
    { label: 'Hourly', value: 'hours' },
  ],
  Leave_TYPE: [
    { label: 'Hours', value: 'Hours' },
    { label: 'Days', value: 'Days' },
  ],
  WAGE_AMOUNT_TYPE: [
    { label: 'Annum', value: 'annum' },
    { label: 'Quarterly (3 months)', value: 'quarterly' },
    { label: 'Month', value: 'month' },
    { label: 'Week', value: 'week' },
    { label: 'Day', value: 'day' },
  ],
  EXPIRY_DATE_DURATION: [
    { label: '2 Month', value: '2_month' },
    { label: '4 Month', value: '4_month' },
    { label: '6 Month', value: '6_month' },
    { label: '1 Year', value: '1_year' },
    { label: 'Custom', value: 'custom' },
  ],
  PLAN_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
    // { value: 'disabled', label: 'Disabled' }
  ],
  PROVIDER_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
    { value: 'deleted', label: 'Deleted' },
  ],
  PLAN_VISIBILITY_STATUS: [
    { value: 'public', label: 'Public' },
    { value: 'private', label: 'Private' },
  ],
  ORG_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
  ],
  HOLIDAY_STATUS: [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'In-Active' },
  ],
  LEAVE_TYPES: [
    {
      className: 'pending-leave',
      colorClass: 'yellow-dot-wrap',
      label: 'Pending',
    },
    {
      className: 'approved-leave',
      colorClass: 'green-dot-wrap',
      label: 'Approved',
    },
    {
      className: 'rejected-leave',
      colorClass: 'red-dot-wrap',
      label: 'Rejected',
    },
    {
      className: 'cancelled-leave',
      colorClass: 'black-dot-wrap',
      label: 'Cancelled',
    },
    {
      className: 'calendar-holiday',
      colorClass: 'blue-dot-wrap',
      label: 'Holiday',
    },
  ],
  ASSIGN_EMP_STATUS: [
    { value: '', label: 'All' },
    { value: 'pending', label: 'Pending' },
    { value: 'active', label: 'Active' },
    { value: 'ongoing', label: 'Ongoing' },
    { value: 'completed', label: 'Completed' },
    { value: 'verified', label: 'Verified' },
    { value: 'deleted', label: 'Deleted' },
    { value: 'rejected', label: 'Rejected' },
    { value: 'cancelled', label: 'Cancelled' },
  ],
  SEO_TITLE: 'Microffice',
  APP_NAME: 'Microffice',
  SUBSCRIPTION_TYPE: [
    { label: 'Subscription', value: 'recursive' },
    { label: 'Add on', value: 'one_time' },
  ],
  SUBSCRIPTION_CATEGORY: [
    { label: 'Core', value: 'core' },
    { label: 'Storage', value: 'storage' },
  ],
  YES_NO_OPTIONS: [
    { label: 'Yes', value: 'yes' },
    { label: 'No', value: 'no' },
  ],
  DAY_MONTH_OPTIONS: [
    { label: 'Days', value: 'days' },
    { label: 'Months', value: 'months' },
  ],
  JOINING_OPTIONS: [
    { label: 'Date of Joining', value: 'date_of_joining' },
    { label: 'After Internship End', value: 'after_internship_end' },
    { label: 'After Probation End', value: 'after_probation_end' },
  ],
  TIME_PERIOD_OPTIONS: [
    {
      label: 'Yearly',
      value: 'yearly',
    },
    {
      label: 'Monthly',
      value: 'monthly',
    },
    {
      label: 'Quarterly',
      value: 'quarterly',
    },
    { label: 'Half-Yearly', value: 'half_yearly' },
    { label: 'One-Time', value: 'one_time' },
  ],
  CARRY_FORWARD_OPTIONS: [
    { label: 'Percentage', value: 'percentage' },
    { label: 'Unit', value: 'unit' },
  ],
  ENCASHMENT_PERIOD_OPTIONS: [
    { label: 'Years', value: 'year' },
    { label: 'Months', value: 'month' },
    { label: 'Week', value: 'week' },
  ],
  LEAVE_DEDUCTION_OPTIONS: [
    { label: 'Percentage', value: 'percentage' },
    { label: 'Unit', value: 'unit' },
    { label: 'Maintaining Leave Days', value: 'maintaing_leave_days' },
  ],
};
