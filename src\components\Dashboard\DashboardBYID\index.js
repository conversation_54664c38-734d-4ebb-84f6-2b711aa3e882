'use client';

import React, { useContext, useEffect, useState } from 'react';
import { Box, Typography, CircularProgress } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import { useRouter } from 'next/navigation';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
// import AutoGraphIcon from '@mui/icons-material/AutoGraph';
// import DonutSmallIcon from '@mui/icons-material/DonutSmall';
// import EqualizerIcon from '@mui/icons-material/Equalizer';
// import InboxIcon from '@mui/icons-material/Inbox';
import DashboardSidebar from '@/components/Dashboard/DashboardList';
import RightDrawer from '@/components/UI/RightDrawer';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import CustomButton from '@/components/UI/CustomButton';
import LineCharts from '@/components/UI/LineChart';
import Bar<PERSON>hart from '@/components/UI/BarChart';
import PieCharts from '@/components/UI/PieChart';
import DashboardIcon from '@mui/icons-material/Dashboard';
import AddIcon from '@mui/icons-material/Add';
import SettingsSuggestIcon from '@mui/icons-material/SettingsSuggest';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import SaveAsIcon from '@mui/icons-material/SaveAs';
import DateFilter from '@/components/DSR/Reports/DateFilter';
import MultipleFilter from '@/components/UI/MultipleFilter';
import DashboardDataView from '../DashDataView';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import moment from 'moment';
import NoDataView from '@/components/UI/NoDataView';
import '../Template/template.scss';

export default function DashboardBYID({
  DashboardData,
  branchList,
  isDefault,
  getOneDashboardList,
  DashbaordId,
  IsDefaultFilter,
  checkLoader,
  setDashboardData,
}) {
  const { authState, setIsDrawer } = useContext(AuthContext);
  const router = useRouter();
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  // const [loader, setLoader] = useState(false);
  const [defaultDashboard, setDefaultDashboard] = useState(false);
  const [screenHeight, setScreenHeight] = useState(null);
  const [screenWidth, setScreenWidth] = useState(null);
  const [customStartDate, setCustomStartDate] = useState(null);
  const [customEndDate, setCustomEndDate] = useState(null);
  const [selectedBranches, setSelectedBranches] = useState([]);
  const [dateSelectedOption, setDateSelectedOption] = useState('today');
  const [TimePeriod, setTimePeriod] = useState('monthly');
  const [dateFilterList, setDateFilterList] = useState([]);
  const [dashboardList, setDashboardList] = useState([]);

  const handleOpenFilterDrawer = () => {
    setOpenFilterDrawer(true);
    setIsDrawer(true);
  };
  const handleCloseFIlterDrawer = () => {
    setOpenFilterDrawer(false);
    setIsDrawer(false);
  };
  // Fetches the list of date filter
  const getDateFilterListDay = async () => {
    // setLoader(true);

    try {
      const { status, data } = await axiosInstance.get(
        `${URLS.DATE_FILTER_LIST}?report_filter_type=day`
      );
      if (status === 200) {
        // setLoader(false);
        const filterList = data?.data?.map((user) => ({
          label: user?.value,
          value: user?.key,
        }));
        const filterLast = filterList.filter(
          (f, i) => i !== filterList?.length - 1
        );
        setDateFilterList(filterLast);
      }
    } catch (error) {
      // setLoader(false);
      setDateFilterList([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const onclickSetDefault = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.put(
        URLS?.MARK_AS_DEFAULT + `${DashboardData?.id}`
      );
      if (status === 200 || status === 201) {
        // setLoader(false);
        setApiMessage('success', data?.message);
        setDefaultDashboard(!defaultDashboard);
      }
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // get Dashboard list
  const getDashboardList = async () => {
    // setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_SAVED_DASHBOARD_LIST
      );
      if (status === 200) {
        // setLoader(false);
        setDashboardList(data?.data);
        const findDefaultDashboard =
          isDefault && data?.data?.find((f) => f?.has_dashboard_default);

        findDefaultDashboard && findDefaultDashboard?.id && isDefault
          ? getOneDashboardList(findDefaultDashboard?.id, '', '', true, '')
          : setDashboardData([]);
      }
    } catch {
      // setLoader(false);
      setDashboardList([]);
    }
  };

  // Delete Dashbaord
  const DeleteDashboard = async (id) => {
    try {
      // setLoader(true);
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_DASHBOARD + id
      );
      if (status === 200) {
        if (data.status) {
          getDashboardList();
          setApiMessage('success', data?.message);
        } else {
          getDashboardList();
          setApiMessage('error', data?.message);
        }
      } else {
        setApiMessage('error', data?.message);
      }
      // setLoader(false);
    } catch (error) {
      // setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  const getDateByfilter = () => {
    if (dateSelectedOption === 'today') {
      return DateFormat(moment().startOf('day'), 'dates');
    } else if (dateSelectedOption === 'yesterday') {
      return DateFormat(moment().subtract(1, 'day').startOf('day'), 'dates');
    } else if (dateSelectedOption === 'this_week') {
      const start = DateFormat(moment().startOf('isoWeek'), 'dates');
      const end = DateFormat(moment().endOf('isoWeek'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_month') {
      const start = DateFormat(moment().startOf('month'), 'dates');
      const end = DateFormat(moment().endOf('month'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'this_quarter') {
      const start = DateFormat(moment().startOf('quarter'), 'dates');
      const end = DateFormat(moment().endOf('quarter'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_week') {
      const start = DateFormat(
        moment().subtract(1, 'week').startOf('isoWeek'),
        'dates'
      );
      const end = DateFormat(
        moment().subtract(1, 'week').endOf('isoWeek'),
        'dates'
      );
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_month') {
      const start = DateFormat(
        moment().subtract(1, 'month').startOf('month'),
        'dates'
      );
      const end = DateFormat(
        moment().subtract(1, 'month').endOf('month'),
        'dates'
      );
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_7_days') {
      const start = DateFormat(
        moment().subtract(6, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_14_days') {
      const start = DateFormat(
        moment().subtract(13, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_30_days') {
      const start = DateFormat(
        moment().subtract(29, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_60_days') {
      const start = DateFormat(
        moment().subtract(59, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else if (dateSelectedOption === 'last_90_days') {
      const start = DateFormat(
        moment().subtract(89, 'days').startOf('day'),
        'dates'
      );
      const end = DateFormat(moment().endOf('day'), 'dates');
      return `${start} - ${end}`;
    } else {
      return '';
    }
  };
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleResize = () => {
        setScreenHeight(window.innerHeight);
        setScreenWidth(window.innerWidth);
      };

      window.addEventListener('resize', handleResize);
      handleResize();
      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [screenHeight, screenWidth]);
  const onDragEnd = () => {};

  useEffect(() => {
    if (DashboardData && !IsDefaultFilter) {
      if (DashboardData?.dashboard_filter) {
        setSelectedBranches(
          DashboardData?.dashboard_filter?.branch_id
            ? DashboardData?.dashboard_filter?.branch_id.split(',').map(Number)
            : []
        );
        setDateSelectedOption(
          DashboardData?.dashboard_filter?.date_filter
            ? DashboardData?.dashboard_filter?.date_filter
            : ''
        );
        setTimePeriod(
          DashboardData?.dashboard_filter?.filter_time_period
            ? DashboardData?.dashboard_filter?.filter_time_period
            : 'monthly'
        );
      }
      setDefaultDashboard(DashboardData?.has_dashboard_default);
    }
  }, [DashboardData]);
  useEffect(() => {
    if (
      authState?.UserPermission?.dashboard === 1 ||
      authState?.UserPermission?.dashboard === 2
    ) {
      getDashboardList();
      getDateFilterListDay();
    }
  }, [authState?.UserPermission?.dashboard]);

  return (
    <>
      <Box
        className={
          isDefault &&
          ((DashboardData &&
            DashboardData?.dashboard_models &&
            DashboardData?.dashboard_models?.length === 0) ||
            !DashboardData?.dashboard_models ||
            !DashboardData)
            ? 'dashboard-page blanck-dashboard-page'
            : 'dashboard-page'
        }
      >
        <Box className="dashboard-list">
          {isDefault ? (
            <Box className="default-dashboard-name-sec">
              <Typography className="title-sm fw600">
                Welcome,{' '}
                {authState &&
                authState?.user_first_name &&
                authState?.user_middle_name &&
                authState?.user_last_name
                  ? authState?.user_first_name +
                    ' ' +
                    authState?.user_middle_name +
                    ' ' +
                    authState?.user_last_name
                  : authState &&
                      authState?.user_first_name &&
                      authState?.user_last_name
                    ? authState?.user_first_name +
                      ' ' +
                      authState?.user_last_name
                    : 'User name'}{' '}
                !
              </Typography>

              {DashboardData && DashboardData?.dashboard_name && (
                <Box className="d-flex default-dashboard align-center">
                  <DashboardIcon />
                  <Typography className="body-text fw600 dashboard-text text-ellipsis-line">
                    {DashboardData?.dashboard_name}
                  </Typography>
                </Box>
              )}
            </Box>
          ) : (
            <Box className="d-flex align-center dashboard-name-sec">
              {' '}
              <ArrowBackIosIcon
                className="mr8 cursor-pointer"
                onClick={() => router.push('/chart-dashboard')}
              />
              <Typography className="title-sm fw600 main-heading dashboard-text text-ellipsis-line">
                {DashboardData && DashboardData?.dashboard_name
                  ? DashboardData?.dashboard_name
                  : '[Dashboard Name]'}
              </Typography>
            </Box>
          )}

          <Box className="d-flex align-center gap-sm">
            {isDefault && dashboardList && dashboardList?.length > 0 && (
              <CustomButton
                variant="outlined"
                startIcon={<FormatListBulletedIcon />}
                title={'Dashboards'}
                onClick={() => handleOpenFilterDrawer()}
              />
            )}
            {!isDefault &&
            authState?.UserPermission?.dashboard === 2 &&
            !DashboardData?.has_dashboard_default &&
            !defaultDashboard ? (
              <CustomButton
                variant="outlined"
                startIcon={<SettingsSuggestIcon />}
                title="Set As Default"
                onClick={() => DashboardData?.id && onclickSetDefault()}
              />
            ) : (
              <></>
            )}
            {isDefault && authState?.UserPermission?.dashboard === 2 && (
              <CustomButton
                variant="outlined"
                startIcon={<AddIcon />}
                title={'Create Dashboard'}
                onClick={() => router.push('/chart-dashboard/template')}
              />
            )}
            {DashboardData &&
              authState?.UserPermission?.dashboard === 2 &&
              DashboardData?.dashboard_models && (
                <CustomButton
                  variant="contained"
                  startIcon={<SaveAsIcon />}
                  title="Edit Dashboard"
                  onClick={() =>
                    router.push(
                      DashboardData?.id
                        ? `/chart-dashboard/template/${DashboardData?.id}`
                        : `/chart-dashboard/template`
                    )
                  }
                />
              )}
          </Box>
        </Box>
        {DashboardData && DashboardData?.dashboard_models && (
          <Box className="dashboard-filter">
            <Box className="subcategories-wrap">
              <Box>
                <DateFilter
                  dateSelectedOption={dateSelectedOption}
                  setDateSelectedOption={setDateSelectedOption}
                  customStartDate={customStartDate}
                  setCustomStartDate={setCustomStartDate}
                  customEndDate={customEndDate}
                  setCustomEndDate={setCustomEndDate}
                  dateFilterList={dateFilterList}
                  type="dsr"
                />
                {getDateByfilter() ? (
                  <span className="title-text">{getDateByfilter()}</span>
                ) : (
                  <></>
                )}
              </Box>
              {/* Branch Filter */}
              <MultipleFilter
                selected={selectedBranches}
                setSelected={setSelectedBranches}
                List={branchList}
                placeholder="Branches"
              />
              <Box className="d-flex align-center gap-sm">
                <CustomButton
                  variant="contained"
                  onClick={() => {
                    DashbaordId &&
                      getOneDashboardList(
                        DashbaordId,
                        dateSelectedOption,
                        selectedBranches && selectedBranches?.length > 0
                          ? selectedBranches.toString()
                          : '',
                        false,
                        TimePeriod
                      );
                  }}
                  title="Apply"
                  disabled={!DashbaordId}
                />
                <CustomButton
                  variant="outlined"
                  onClick={() => {
                    setDateSelectedOption('today');
                    setSelectedBranches([]);
                    DashbaordId &&
                      getOneDashboardList(
                        DashbaordId,
                        'today',
                        '',
                        false,
                        TimePeriod
                      );
                  }}
                  title="Clear"
                  disabled={!DashbaordId}
                />
              </Box>
            </Box>

            <Box className="timperiod-filters d-flex align-center justify-end">
              <CustomButton
                variant={TimePeriod === 'yearly' ? 'contained' : 'outlined'}
                onClick={() => {
                  setTimePeriod('yearly');
                }}
                title="Year"
              />
              <CustomButton
                variant={TimePeriod === 'quarterly' ? 'contained' : 'outlined'}
                onClick={() => {
                  setTimePeriod('quarterly');
                }}
                title="Quarter"
              />
              <CustomButton
                variant={TimePeriod === 'monthly' ? 'contained' : 'outlined'}
                onClick={() => {
                  setTimePeriod('monthly');
                }}
                title="Month"
              />
              <CustomButton
                variant={TimePeriod === 'weekly' ? 'contained' : 'outlined'}
                onClick={() => {
                  setTimePeriod('weekly');
                }}
                title="Week"
              />
              <CustomButton
                variant={TimePeriod === 'daily' ? 'contained' : 'outlined'}
                onClick={() => {
                  setTimePeriod('daily');
                }}
                title="Day"
              />
            </Box>
          </Box>
        )}

        {checkLoader || DashboardData === undefined ? (
          <Box className="content-loader">
            <CircularProgress className="loader" color="inherit" />
          </Box>
        ) : DashboardData && DashboardData?.dashboard_models ? (
          <Box className="Dashboard-template-models">
            <DragDropContext onDragEnd={onDragEnd}>
              {DashboardData &&
              DashboardData?.dashboard_models &&
              DashboardData?.dashboard_models?.length > 0 ? (
                <>
                  <Droppable droppableId="droppable">
                    {(provided) => (
                      <Box
                        {...provided.droppableProps}
                        ref={provided.innerRef}
                        sx={{
                          display: 'flex',
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          gap: 2,
                        }}
                      >
                        {DashboardData?.dashboard_models?.map((item, index) => {
                          return (
                            <Draggable
                              key={item.id}
                              draggableId={item.id.toString()}
                              index={index}
                              // isDragDisabled={disable}
                            >
                              {(provided) => {
                                return (
                                  <Box
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    className={`dashboard-model-view ${
                                      item?.model_type === 'number'
                                        ? 'dashboard-numbers-section'
                                        : item?.model_type === 'pie_chart'
                                          ? 'dashboard-piechart-section'
                                          : 'dashboard-chart-section'
                                    }`}
                                  >
                                    {/* <Box className="Edit-drag-icon">
                                     <Box
                                        className="cursor-pointer"
                                        onClick={() => handleOpenModel(true)}
                                      >
                                        <EditIcon />
                                      </Box> 

                                      <Box {...provided.dragHandleProps}>
                                        <DragIcon />
                                      </Box> 
                                    </Box>*/}

                                    {item?.model_type === 'line_chart' ? (
                                      <Box>
                                        <Typography className="body-text">
                                          {item?.Name}
                                        </Typography>
                                        <LineCharts
                                          item={item?.dashboard_data}
                                        />
                                      </Box>
                                    ) : item?.model_type === 'bar_chart' ? (
                                      <Box>
                                        <Typography className="body-text">
                                          {item?.Name}
                                        </Typography>
                                        <BarChart item={item?.dashboard_data} />
                                      </Box>
                                    ) : item?.model_type === 'pie_chart' ? (
                                      <Box>
                                        <Typography className="body-text">
                                          {item?.Name}
                                        </Typography>
                                        <PieCharts
                                          item={item?.dashboard_data}
                                          piechartsData={item}
                                          screenWidth={screenWidth}
                                        />
                                      </Box>
                                    ) : item?.model_type === 'number' ? (
                                      <Box className="">
                                        {/* <Typography className="body-text">
                                          {item?.Name}
                                        </Typography> */}
                                        <DashboardDataView
                                          item={item}
                                          isFromDashboard={true}
                                        />
                                      </Box>
                                    ) : (
                                      <></>
                                    )}
                                  </Box>
                                );
                              }}
                            </Draggable>
                          );
                        })}
                      </Box>
                    )}
                  </Droppable>
                </>
              ) : (
                <Box className="no-data d-flex align-center justify-center">
                  <NoDataView
                    title="No Dashboard Found"
                    description="There is no dashboard available at the moment."
                  />
                </Box>
              )}
            </DragDropContext>
          </Box>
        ) : (
          <Box className="">
            {/* <Box className={'blank-dashboard '}>
              <Box>
                <AutoGraphIcon className="graph-icon" />
                <DonutSmallIcon className="graph-icon" />
              </Box>
              <Box>
                <EqualizerIcon className="graph-icon" />
                <InboxIcon className="graph-icon" />
              </Box>

              <Typography className="title-sm blank-text ">
                No dashboards available.
              </Typography>
              {authState?.web_user_active_role_id === 7 ||
              authState?.web_user_active_role_id === 14 ? (
                <Typography className="title-sm blank-text">
                  Please contact Admin!
                </Typography>
              ) : (
                <Typography className="title-sm blank-text">
                  Click "Create Dashboard" to get started! 🚀
                </Typography>
              )}
            </Box> */}
            <Box className="no-data d-flex align-center justify-center">
              <NoDataView
                title="No dashboards available."
                description={
                  authState?.web_user_active_role_id === 7 ||
                  authState?.web_user_active_role_id === 14
                    ? 'Please contact Admin!'
                    : ' Click "Create Dashboard" to get started!'
                }
              />
            </Box>
          </Box>
        )}
      </Box>

      <RightDrawer
        anchor={'right'}
        open={openFilterDrawer}
        onClose={handleCloseFIlterDrawer}
        title={'Dashboard'}
        content={
          <DashboardSidebar
            dashboardList={dashboardList}
            DeleteDashboard={DeleteDashboard}
          />
        }
      />
    </>
  );
}
