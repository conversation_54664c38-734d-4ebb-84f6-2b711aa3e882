import { EmptyTicketIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import React from 'react';
import './notickets.scss';
import CustomButton from '@/components/UI/CustomButton';

export default function NoTickets({ onAddTicketClick }) {
  return (
    <Box className="no-ticket-wrap d-flex flex-col align-center">
      <Box className="ticket-icon-wrap">
        <EmptyTicketIcon />
      </Box>
      <Typography className="no-ticket-text-wrap title-sm">
        You don't have any Tickets
      </Typography>
      <Typography className="text-wrap body-sm">
        Get started by adding new Tickets to your help desk
      </Typography>
      <Box className="pt16">
        <CustomButton
          type="button"
          variant="contained"
          title="Create Ticket"
          onClick={onAddTicketClick} // Trigger the function passed as prop
        />
      </Box>
    </Box>
  );
}
