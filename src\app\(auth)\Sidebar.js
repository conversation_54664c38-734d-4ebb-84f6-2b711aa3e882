'use client';

import React, { useEffect, useState, useContext } from 'react';
import {
  Box,
  ListItemIcon,
  ListItemText,
  MenuList,
  MenuItem,
  // IconButton,
  // Tooltip,
  Typography,
} from '@mui/material';
// import MenuIcon from '@mui/icons-material/Menu';
// import ArrowBackIosNewIcon from '@mui/icons-material/ArrowBackIosNew';
import AuthContext from '@/helper/authcontext';
import { useRouter, usePathname, useSearchParams } from 'next/navigation';
import {
  removeFromStorage,
  saveToStorage,
  fetchFromStorage,
} from '@/helper/context';
import { identifiers } from '@/helper/constants/identifier';
import { leftSideMenu } from '@/helper/common/commonMenus';
import AppBrandLogo from '@/components/UI/AppBrandLogo';
import { checkOrganizationRole } from '@/helper/common/commonFunctions';

const Sidebar = ({ open }) => {
  // setOpen
  const {
    orgDetails,
    setUserdata,
    authState,
    reFetch,
    setReFetch,
    isActive,
    setIsActive,
    setRestrictedModal,
  } = useContext(AuthContext);
  const [openMenu, setOpenMenu] = useState({});
  const [permissionLink, setPermissionLink] = useState();
  const searchParams = useSearchParams();
  const IsADMIN = searchParams.get('IsAdmin');
  const IsInvite = searchParams.get('IsInvite');
  const router = useRouter();
  const pathname = usePathname();
  const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const isNormalUser = fetchFromStorage(identifiers?.NORMAL_LOGIN);
  const isNormalLogin =
    authdata?.organizationId && authdata?.organizationStatus;
  const handleClick = (item) => {
    const isOrgView = checkOrganizationRole('org_master') || false;
    const isRestricted = isNormalUser
      ? false
      : isOrgView
        ? !orgDetails?.attributes?.email ||
          !orgDetails?.attributes?.contact_person ||
          authState?.purchase_plan === false
        : !authState?.profile_status || authState?.purchase_plan === false;
    if (isRestricted) {
      if (isOrgView) {
        if (pathname === '/org/organization') {
          setRestrictedModal({
            isOrgView: isOrgView,
            purchase_plan: authState?.purchase_plan,
            user_status:
              !orgDetails?.attributes?.email ||
              !orgDetails?.attributes?.contact_person,
            profile_status: authState?.profile_status,
          });
        } else {
          router.push('/org/organization');
        }
      } else {
        if (pathname === '/myprofile') {
          setRestrictedModal({
            isOrgView: isOrgView,
            purchase_plan: authState?.purchase_plan,
            profile_status: authState?.profile_status,
          });
        } else {
          router.push('/myprofile');
        }
      }
    } else {
      setUserdata();
      removeFromStorage(identifiers?.RedirectData);
      removeFromStorage(identifiers?.LOGIN_ORG);
      setOpenMenu((prevState) => ({
        ...openMenu,
        [item?.name]: !prevState[item?.name],
      }));
      setReFetch(!reFetch);
      saveToStorage('activeMenuItem', item?.id);
      setIsActive(item?.id);
      if (item?.link) {
        router.push(item?.link);
      }
    }
  };

  const SideBarMenuList = (menu) => {
    return (
      <>
        {menu
          ?.filter((item) => (isNormalLogin ? item : item?.id !== 8))
          ?.filter((item) => item?.id !== 5) // for hide notification menu in sidebar
          ?.map((item, index) => {
            if (
              authState &&
              authState?.UserPermission &&
              item?.permission &&
              (authState?.UserPermission?.[item?.permission] === 2 ||
                authState?.UserPermission?.[item?.permission] === 1 ||
                item?.permission === 'leavecenter' ||
                (item?.permission === 'dashboard' &&
                  (authState?.UserPermission?.['forecast'] === 2 ||
                    authState?.UserPermission?.['forecast'] === 1)) ||
                (item?.permission === 'notificationcenter' && item?.id === 5) ||
                (item?.permission === 'notificationcenter' &&
                  item?.id === 2 &&
                  (checkOrganizationRole('org_master') ||
                    checkOrganizationRole('super_admin') ||
                    authState?.web_user_active_role_id !== 1)) ||
                (item?.permission === 'resignationcenter' && item?.id === 9) ||
                (item?.permission === 'resignationcenter' &&
                  item?.id === 2 &&
                  authState?.web_user_active_role_id !== 1) ||
                (item?.permission === 'staffpermission' &&
                  (authState?.UserPermission?.['staff'] === 1 ||
                    authState?.UserPermission?.['staff'] === 2)) ||
                item?.permission === 'change_request' ||
                item?.permission === 'owncategory' ||
                (item?.permission === 'category' && item?.id === 15))
            ) {
              return (
                <>
                  {/* <Tooltip
                    title={item?.name}
                    placement="right"
                    classes={{
                      tooltip: open ? 'd-none' : 'sidebar-list-tooltip',
                    }}
                    arrow
                    key={index}
                  > */}
                  <MenuItem
                    key={index}
                    className={isActive === item?.id ? 'active' : ''}
                    onClick={() => {
                      // setIsActive(item?.id);
                      // item?.link && router?.push(item?.link);
                      handleClick(item);
                    }}
                    role="complementary"
                  >
                    <ListItemText>
                      {item?.icon && (
                        <Box className="d-flex align-center">
                          <ListItemIcon className="menu-icon">
                            <Box className="icon">{item?.icon}</Box>
                          </ListItemIcon>
                        </Box>
                      )}
                      <Box className="d-flex align-center justify-space-between">
                        <Typography className="sub-title-text menu-name">
                          {item?.name}
                        </Typography>
                      </Box>
                    </ListItemText>
                  </MenuItem>
                  {/* </Tooltip> */}
                </>
              );
            } else {
              return <React.Fragment key={index}></React.Fragment>;
            }
          })}
      </>
    );
  };

  useEffect(() => {
    const activetab = leftSideMenu.find(
      (f) =>
        window.location.pathname.startsWith(f?.link) &&
        window.location.pathname.endsWith(f?.link)
    );
    activetab?.id && setIsActive(activetab?.id);
    const pathsToCheck = [
      '/own-leave',
      '/leave-remark',
      '/staff-notification',
      '/own-notification',
      '/resignation-remark',
      '/resignation',
      '/dsr',
      '/wsr',
      '/payroll',
      '/logbook-reports',
      '/staff',
      '/invited-staff',
      '/empcontracts',
      '/contract-renewal',
      '/leave-policy',
      '/leave-policy-type',
      '/contract-type',
      '/budget-forecast',
      '/chart-dashboard',
      '/leave-reports',
      '/org/setup',
      '/support-ticket',
    ];
    const isPathIncluded = pathsToCheck.some((path) =>
      window.location.pathname.includes(path)
    );
    if (window.location.pathname.includes('/user/') && IsADMIN) {
      setIsActive(2);
      setOpenMenu({ ...openMenu, 'Admin User': true });
    } else if (
      window.location.pathname.includes('/create-emp-contract') ||
      window.location.pathname.includes('/edit-emp-contract')
    ) {
      setIsActive(14);
      setOpenMenu({
        ...openMenu,
        'Contract Center': true,
      });
    } else if (window.location.pathname.includes('/org/setup')) {
      setIsActive(17);
      setOpenMenu({
        ...openMenu,
        Setup: true,
      });
    } else if (window.location.pathname.includes('/user/') && IsInvite) {
      setIsActive(6);
      setOpenMenu({ ...openMenu, Staff: true, 'Staff Invitation': true });
    } else if (window.location.pathname.includes('/user/')) {
      setIsActive(6);
      setOpenMenu({ ...openMenu, Staff: true, 'All staff': true });
    } else if (
      window.location.pathname.includes('/change-req') ||
      window.location.pathname.includes('/change-request')
    ) {
      setIsActive(11);
      setOpenMenu({ 'Change Request': true });
    } else if (
      window.location.pathname.includes('/document-staff/') ||
      window.location.pathname.includes('/create-category') ||
      window.location.pathname.includes('/create-content')
    ) {
      setIsActive(15);
      setOpenMenu({ 'Document Center': true, 'Staff view': true });
    } else if (window.location.pathname.includes('/document-own/')) {
      setIsActive(15);
      setOpenMenu({ 'Document Center': true, 'Personal view': true });
    } else if (window.location.pathname.includes('/branch/')) {
      setIsActive(3);
      setOpenMenu({ Branch: true });
    } else if (
      window.location.pathname.includes('/chart-dashboard/') ||
      window.location.pathname.includes('/chart-dashboard/template') ||
      window.location.pathname.includes('/budget-forecast/')
    ) {
      setIsActive(1);
      setOpenMenu({ Dashboard: true });
    } else if (
      window.location.pathname.includes('/rota-deleted-shifts') ||
      window.location.pathname.includes('/rota-employees-order')
    ) {
      setIsActive(8);
      setOpenMenu({ Rotas: true });
    } else if (window.location.pathname.includes('/support-ticket/')) {
      setIsActive(19);
      if (window.location.pathname.includes('/support-ticket/dashboard')) {
        setOpenMenu({ 'Support Tickets': true, Dashboard: true });
      } else if (
        window.location.pathname.includes('/support-ticket/all-tickets')
      ) {
        setOpenMenu({ 'Support Tickets': true, 'All Tickets': true });
      } else if (
        window.location.pathname.includes('/support-ticket/create-ticket')
      ) {
        setIsActive(18);
        setOpenMenu({});
      } else if (window.location.pathname === '/support-ticket') {
        setOpenMenu({ 'Support Tickets': true });
      } else {
        setOpenMenu({ 'Support Tickets': true });
      }
    }

    if (isPathIncluded) {
      const seltab = leftSideMenu.find((f) => {
        return (
          f?.submenu &&
          f?.submenu.length > 0 &&
          f?.submenu.find((s) => window.location.pathname === s?.link)
        );
      });
      const selSub = seltab?.submenu?.find(
        (s) => window.location.pathname === s?.link
      );
      if (seltab) {
        setIsActive(seltab?.id);
        setOpenMenu({ [selSub?.name]: true, [seltab?.name]: true });
      } else if (window.location.pathname.includes('/play-list/')) {
        setIsActive(10);
        setOpenMenu({ 'Document Center': true, Folders: true });
      } else if (
        window.location.pathname.includes('/dsr/') ||
        window.location.pathname.includes('/wsr/') ||
        window.location.pathname.includes('/payroll/')
      ) {
        setIsActive(10);
        setOpenMenu({ 'Logs Book': true });
      }
    }
  }, [pathname]);
  useEffect(() => {
    if (
      authState?.UserPermission &&
      authState?.UserPermission?.category === 0 &&
      pathname.startsWith('/document-staff/all')
    ) {
      router?.push('/document-own/all');
      setPermissionLink('/document-own/all');
    }
    if (
      pathname === '/myprofile' ||
      pathname === '/org/organization' ||
      pathname === '/sorg/organization'
    ) {
      removeFromStorage('activeMenuItem');
      setOpenMenu({});
      setIsActive();
    }
  }, [pathname]);
  useEffect(() => {
    if (
      authState?.UserPermission?.dashboard === 2 ||
      authState?.UserPermission?.dashboard === 1 ||
      authState?.UserPermission?.forecast === 2 ||
      authState?.UserPermission?.forecast === 1
    ) {
      if (
        window.location.pathname.startsWith('/budget-forecast') &&
        authState?.UserPermission &&
        authState?.UserPermission?.forecast === 0 &&
        (authState?.UserPermission?.dashboard === 2 ||
          authState?.UserPermission?.dashboard === 1)
      ) {
        router?.push('/chart-dashboard');
        setPermissionLink('/chart-dashboard');
      } else if (
        window.location.pathname.startsWith('/chart-dashboard') &&
        authState?.UserPermission &&
        authState?.UserPermission?.dashboard === 0 &&
        (authState?.UserPermission?.forecast === 2 ||
          authState?.UserPermission?.forecast === 1)
      ) {
        router?.push('/budget-forecast');
        setPermissionLink('/budget-forecast');
      }
    }
  }, [pathname]);
  useEffect(() => {
    if (authState?.UserPermission) {
      const activetab = leftSideMenu.find(
        (f) =>
          window.location.pathname.startsWith(f?.link) &&
          window.location.pathname.endsWith(f?.link)
      );
      const permissionAccess = leftSideMenu?.find(
        (f) =>
          authState?.UserPermission?.[f?.permission] !== 0 &&
          authState?.UserPermission?.[f?.permission] !== '' &&
          authState?.UserPermission?.[f?.permission] !== undefined &&
          f?.link !== undefined &&
          f?.permission !== ''
      );
      var permissionSubActive = null;
      leftSideMenu?.find((f) => {
        if (
          authState?.UserPermission?.[f?.permission] !== 0 &&
          authState?.UserPermission?.[f?.permission] !== '' &&
          authState?.UserPermission?.[f?.permission] !== undefined &&
          f?.link !== undefined &&
          f?.permission !== ''
        ) {
          return f;
        } else {
          const subpermission = f?.submenu?.find(
            (fs) =>
              authState?.UserPermission?.[fs?.permission] !== 0 &&
              authState?.UserPermission?.[fs?.permission] !== '' &&
              authState?.UserPermission?.[fs?.permission] !== undefined &&
              fs?.link !== undefined &&
              fs?.permission !== ''
          );
          permissionSubActive = subpermission;
          if (subpermission) {
            return subpermission;
          }
        }
      });

      let actvivesubmenu = null;
      leftSideMenu.find((f) =>
        f?.submenu?.find((s) => {
          if (
            window.location.pathname.startsWith(s?.link) &&
            window.location.pathname.endsWith(s?.link)
          ) {
            actvivesubmenu = s;
          }
        })
      );
      if (
        window.location.pathname.includes('/change-req') ||
        window.location.pathname.includes('/change-request')
      ) {
        //
      } else if (
        actvivesubmenu?.permission === 'notificationcenter' ||
        actvivesubmenu?.permission === 'resignationcenter' ||
        actvivesubmenu?.permission === 'leavecenter' ||
        actvivesubmenu?.permission === 'owncategory'
      ) {
        //
      } else if (
        window.location.pathname.startsWith('/budget-forecast') &&
        authState?.UserPermission &&
        authState?.UserPermission?.forecast === 0 &&
        (authState?.UserPermission?.dashboard === 2 ||
          authState?.UserPermission?.dashboard === 1)
      ) {
        router?.push('/chart-dashboard');
        setPermissionLink('/chart-dashboard');
      } else if (
        window.location.pathname.startsWith('/chart-dashboard') &&
        authState?.UserPermission &&
        authState?.UserPermission?.dashboard === 0 &&
        (authState?.UserPermission?.forecast === 2 ||
          authState?.UserPermission?.forecast === 1)
      ) {
        router?.push('/budget-forecast');
        setPermissionLink('/budget-forecast');
      } else if (
        activetab &&
        (permissionAccess?.link || permissionSubActive?.link) &&
        (authState?.UserPermission?.[activetab?.permission] === 0 ||
          authState?.UserPermission?.[activetab?.permission] === undefined)
      ) {
        permissionAccess?.link
          ? router?.push(`${permissionAccess?.link}`)
          : router?.push(`${permissionSubActive?.link}`);
        permissionAccess?.link
          ? setPermissionLink(permissionAccess?.link)
          : setPermissionLink(permissionSubActive?.link);
      } else if (
        actvivesubmenu &&
        (permissionAccess?.link || permissionSubActive?.link) &&
        (authState?.UserPermission?.[actvivesubmenu?.permission] === 0 ||
          authState?.UserPermission?.[actvivesubmenu?.permission] === undefined)
      ) {
        permissionAccess?.link
          ? router?.push(`${permissionAccess?.link}`)
          : router?.push(`${permissionSubActive?.link}`);
        permissionAccess?.link
          ? setPermissionLink(permissionAccess?.link)
          : setPermissionLink(permissionSubActive?.link);
      } else if (
        window.location.pathname.startsWith('/branch/') &&
        (permissionAccess?.link || permissionSubActive?.link) &&
        (authState?.UserPermission?.branch === 0 ||
          authState?.UserPermission?.branch === 1)
      ) {
        permissionAccess?.link
          ? router?.push(`${permissionAccess?.link}`)
          : router?.push(`${permissionSubActive?.link}`);
        permissionAccess?.link
          ? setPermissionLink(permissionAccess?.link)
          : setPermissionLink(permissionSubActive?.link);
      }
    }
  }, [authState?.UserPermission]);
  return (
    <>
      <Box className="Menu-bar">
        <nav
          className={`${open ? 'menu-list menu-list-open notification-banner' : 'menu-list notification-banner'}`}
        >
          <Box>
            <AppBrandLogo
              className="pt16 pb16 text-center"
              logoClassName="side-menu-logo"
              onClick={() => {
                if (
                  authState?.UserPermission &&
                  authState?.UserPermission?.['dashboard'] !== 0 &&
                  authState?.UserPermission?.['dashboard'] !== undefined
                ) {
                  router.push('/chart-dashboard');
                } else {
                  if (permissionLink) {
                    router.push(permissionLink);
                  } else {
                    router.push('/own-notification');
                  }
                }
              }}
            />
            <MenuList
              className={open ? 'menu-ul-list' : 'menu-ul-list pt0'}
              role="sidebar"
            >
              {/* {open ? (
                <Box className="header-close-section d-flex align-center justify-center">
                  <AppBrandLogo
                    className="pt8 text-center"
                    logoClassName="side-menu-logo"
                    onClick={() => {
                      if (
                        authState?.UserPermission &&
                        authState?.UserPermission?.['dashboard'] !== 0 &&
                        authState?.UserPermission?.['dashboard'] !== undefined
                      ) {
                        router.push('/chart-dashboard');
                      } else {
                        if (permissionLink) {
                          router.push(permissionLink);
                        } else {
                          router.push('/own-notification');
                        }
                      }
                    }}
                  />
                  <Box
                    className="close-icon"
                    onClick={() => {
                      setOpen(false);
                    }}
                  >
                    <ArrowBackIosNewIcon className="cursor-pointer" />
                  </Box>
                </Box>
              ) : (
                <Box
                  sx={{ display: { xs: 'none', md: 'flex' } }}
                  className="mb24"
                >
                  <IconButton
                    size="large"
                    aria-label="account of current user"
                    aria-controls="menu-appbar"
                    aria-haspopup="true"
                    onClick={() => setOpen(true)}
                    color="inherit"
                    className="menu-icon-drawer"
                  >
                    <MenuIcon />
                  </IconButton>
                </Box>
              )} */}

              {SideBarMenuList(leftSideMenu)}
            </MenuList>
          </Box>
        </nav>
      </Box>
    </>
  );
};

export default Sidebar;
