'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import './recenttickets.scss';

const RecentTickets = ({ tickets = [] }) => {
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'status-yellow',
      'in progress': 'draft',
      resolved: 'active-onboarding',
      closed: 'success',
    };
    return statusMap[status] || 'success';
  };

  const getPriorityClass = (priority) => {
    const priorityMap = {
      urgent: 'failed',
      high: 'failed',
      medium: 'draft',
      low: 'active-onboarding',
    };
    return priorityMap[priority] || 'success';
  };

  return (
    <Box className="recent-tickets">
      <Box className="recent-tickets__header">
        <Typography className="title-sm">Recent Tickets</Typography>
      </Box>

      <Box className="recent-tickets__content">
        {tickets.length > 0 ? (
          <Box className="recent-tickets__list">
            {tickets.map((ticket) => (
              <Box key={ticket.id} className="recent-tickets__item">
                <Box className="recent-tickets__item-header">
                  <Typography className="title-text">{ticket.title}</Typography>
                  <Typography className="sub-title-text">
                    {ticket.date}
                  </Typography>
                </Box>

                <Box className="recent-tickets__item-footer">
                  <Box className="recent-tickets__item-badges">
                    <Typography
                      className={`sub-title-text fw600 ${getStatusClass(ticket.status)}`}
                    >
                      {ticket.status}
                    </Typography>
                    <Typography
                      className={`sub-title-text fw600 ${getPriorityClass(ticket.priority)}`}
                    >
                      {ticket.priority}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Box className="recent-tickets__empty">
            <Typography className="content-text">
              No recent tickets found
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default RecentTickets;
