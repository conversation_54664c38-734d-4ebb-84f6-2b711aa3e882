'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import Icon from '@/components/UI/AppIcon/AppIcon';
import './urgenttickets.scss';

const UrgentTickets = ({ tickets = [] }) => {
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'status-yellow',
      'in progress': 'draft',
      resolved: 'active-onboarding',
      closed: 'success',
    };
    return statusMap[status] || 'success';
  };

  return (
    <Box className="urgent-tickets">
      <Box className="urgent-tickets__header">
        <Box className="urgent-tickets__header-content">
          <Icon
            name="AlertTriangle"
            size={20}
            className="urgent-tickets__icon"
          />
          <Typography className="urgent-tickets__title">
            Urgent Tickets
          </Typography>
        </Box>
      </Box>

      <Box className="urgent-tickets__content">
        {tickets.length > 0 ? (
          <Box className="urgent-tickets__list">
            {tickets.map((ticket) => (
              <Box key={ticket.id} className="urgent-tickets__item">
                <Box className="urgent-tickets__item-header">
                  <Typography className="urgent-tickets__item-title">
                    {ticket.title}
                  </Typography>
                </Box>

                <Box className="urgent-tickets__item-footer">
                  <Typography
                    className={`sub-title-text fw600 ${getStatusClass(ticket.status)}`}
                  >
                    {ticket.status}
                  </Typography>
                  <Typography className="urgent-tickets__item-date">
                    {ticket.date}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        ) : (
          <Box className="urgent-tickets__empty">
            <Icon
              name="CheckCircle"
              size={48}
              className="urgent-tickets__empty-icon"
            />
            <Typography className="urgent-tickets__empty-title">
              No Urgent Tickets
            </Typography>
            <Typography className="urgent-tickets__empty-text">
              All urgent tickets have been resolved
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default UrgentTickets;
