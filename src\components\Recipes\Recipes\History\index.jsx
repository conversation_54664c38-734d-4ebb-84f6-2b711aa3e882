'use client';

import React, { useEffect, useState } from 'react';
import { Box, Divider, Tooltip, Typography } from '@mui/material';
import { setApiMessage } from '@/helper/common/commonFunctions';
import PreLoader from '@/components/UI/Loader';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import moment from 'moment';
import ViewIcon from '@/components/ActionIcons/ViewIcon';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import InfiniteScroll from 'react-infinite-scroll-component';
import CloseIcon from '@mui/icons-material/Close';
import HistoryIcon from '@mui/icons-material/History';
import { getRecipeHistory } from '@/services/recipeService';
import NoDataView from '@/components/UI/NoDataView';
import ContentLoader from '@/components/UI/ContentLoader';
import './history.scss';

export default function RecipeHistory({ isView }) {
  const router = useRouter();
  const { slug } = useParams();
  const searchParams = useSearchParams();
  const [recipeData, setRecipeData] = useState([]);
  const [loader, setLoader] = useState(false);
  const [isViewHistory, setIsViewHistory] = useState(false);
  const [recipeVersionData, setRecipeVersionData] = useState(null);
  const isHistory = searchParams.get('history');

  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  // GET RECIPE HISTORY
  const getRecipeHistoryData = async (pageNumber = 1) => {
    recipeData?.length === 0 && setLoader(true);
    try {
      const response = await getRecipeHistory(slug, pageNumber, 10, isView);
      const historyData = response.history;

      setRecipeData((prevData) => [...prevData, ...historyData]);
      setHasMore(historyData?.length > 0);
      setLoader(false);
    } catch (error) {
      setLoader(false);
      setRecipeData([]);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getRecipeHistoryData(page);
  }, [page]);

  const handleViewVersion = (item) => {
    setRecipeVersionData(item);
    if (isView === 'activity') {
      router.push(`/recipes/recipe-activity/${slug}?version=${item?.id}`);
    } else {
      router.push(`/recipes/recipe-history/${slug}?version=${item?.id}`);
    }
  };

  const fetchMoreData = () => {
    setPage((prevPage) => prevPage + 1);
  };

  const latestVersion = recipeVersionData || recipeData?.[0];

  const handleRecipe = (type) => {
    if (type === 'edit') {
      router.push(`/recipes/recipe-update/${slug}`);
    } else if (type === 'history') {
      setIsViewHistory(!isViewHistory);
    }
  };

  useEffect(() => {
    if (isHistory) {
      setIsViewHistory(true);
    }
  }, [isHistory]);

  return (
    <>
      <Box className="recipe-history-main-container">
        <Box className={`h100 ${isViewHistory ? 'd-flex' : ''}`}>
          <Box className="recipe-history-container">
            {loader ? (
              <PreLoader />
            ) : recipeData.length === 0 ? (
              <NoDataView
                title="No recipe history found"
                description="There is no recipe history available at the moment."
              />
            ) : (
              <Box>
                <Box className="d-flex justify-space-between align-center version-title-header">
                  <Box className="d-flex align-center">
                    <ArrowBackIosIcon
                      className="cursor-pointer mt4"
                      onClick={() => {
                        router.push('/recipes');
                      }}
                    />
                    <Box>
                      <Typography className="title-sm fw600">
                        {latestVersion?.recipe_title}
                      </Typography>
                      <Typography className="title-text">
                        Current version as of{' '}
                        {latestVersion?.created_at
                          ? moment(latestVersion?.created_at)?.format(
                              'YYYY-MM-DD HH:mm'
                            )
                          : ''}
                      </Typography>
                    </Box>
                  </Box>
                  <Box className="d-flex justify-start align-center gap-sm recipe-history-btn-list">
                    {/* <Box
                      className="d-flex recipe-history-btn"
                      onClick={() => handleRecipe('edit')}
                    >
                      <Tooltip
                        title={<Typography>Edit</Typography>}
                        arrow
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <DriveFileRenameOutlineIcon />
                      </Tooltip>
                    </Box>
                    <Divider orientation="vertical" flexItem /> */}
                    <Box
                      className="d-flex recipe-history-btn"
                      onClick={() => handleRecipe('history')}
                    >
                      <Tooltip
                        title={<Typography>Version History</Typography>}
                        arrow
                        classes={{ tooltip: 'info-tooltip-container' }}
                      >
                        <HistoryIcon />
                      </Tooltip>
                    </Box>
                  </Box>
                </Box>
                <Divider className="mb16" />
                <Box
                  className="recipe-history-content"
                  dangerouslySetInnerHTML={{
                    __html: latestVersion.changes.replace(/\n/g, '<br/>'),
                  }}
                />
              </Box>
            )}
          </Box>
          {isViewHistory && (
            <Box className="right-sidebar-container">
              <Box className="d-flex justify-space-between align-center recipe-sidebar-header-container">
                <Typography className="title-sm fw600 recipe-sidebar-header">
                  Version History
                </Typography>
                <Box
                  className="pr16 cursor-pointer"
                  onClick={() => setIsViewHistory(!isViewHistory)}
                >
                  <CloseIcon />
                </Box>
              </Box>
              <Box
                id="scrollableSidebar"
                style={{
                  height:
                    'calc(100vh - 150px - var(--banner-height) - var(--spacing-xxl))',
                  overflow: 'auto',
                }}
              >
                <InfiniteScroll
                  dataLength={recipeData?.length}
                  next={fetchMoreData}
                  hasMore={hasMore}
                  loader={
                    <Box>
                      <ContentLoader />
                    </Box>
                  }
                  scrollableTarget="scrollableSidebar"
                >
                  {recipeData &&
                    recipeData?.map((item, index) => {
                      return (
                        <Box
                          key={index}
                          className={`d-flex justify-space-between align-center gap-sm pb16 cursor-pointer history-log ${
                            item?.id === latestVersion?.id ? 'active' : ''
                          }`}
                          onClick={() => handleViewVersion(item)}
                        >
                          <Box>
                            <Typography className="title-text fw600">
                              {item?.created_at
                                ? moment(item?.created_at)?.format(
                                    'YYYY-MM-DD HH:mm'
                                  )
                                : null}
                            </Typography>
                            <Tooltip
                              title={
                                <Typography>{item?.recipe_title}</Typography>
                              }
                              arrow
                              classes={{ tooltip: 'info-tooltip-container' }}
                            >
                              <Typography className="title-text history-log-remark">
                                {item?.recipe_title}
                              </Typography>
                            </Tooltip>
                          </Box>
                          <Box
                            className="cursor-pointer"
                            onClick={() => handleViewVersion(item)}
                          >
                            <ViewIcon />
                          </Box>
                        </Box>
                      );
                    })}
                </InfiniteScroll>
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </>
  );
}
