.filter-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: auto;
  max-height: 80vh;
  background-color: var(--color-white);
  border-radius: var(--border-radius-md) var(--border-radius-md) 0 0;
  box-shadow: var(--box-shadow-xs);
  border: 1px solid var(--border-color-light-gray);
  z-index: 50;
  transition: transform 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), opacity 0.3s ease, visibility 0.3s ease;
  transform: translateY(100%);
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  overflow-y: auto;
  will-change: transform, opacity, visibility;

  @media (min-width: 768px) {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
    width: 0;
    max-height: none;
    border-radius: var(--border-radius-md);
    transform: translateX(0);
    visibility: hidden;
    pointer-events: none;
    z-index: auto;
    overflow: hidden;
    transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1), opacity 0.3s ease, visibility 0.3s ease;
    
    &.filter-panel--open {
      width: 320px;
      visibility: visible;
      pointer-events: auto;
    }
  }

  &--open {
    transform: translateY(0);
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
  }

  &__overlay {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 40;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;

    &--visible {
      opacity: 1;
      visibility: visible;
      pointer-events: auto;
    }

    @media (min-width: 768px) {
      display: none;
    }
  }

  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--border-color-light-gray);
    position: sticky;
    top: 0;
    background-color: var(--color-white);
    z-index: 10;
  }

  &__title {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    color: var(--text-color-black);
    margin: 0;
  }

  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
  }

  &__clear-btn {
    font-size: var(--font-size-sm);
    color: var(--text-color-black);
    background: transparent;
    border: none;
    cursor: pointer;
    text-decoration: underline;
    text-underline-offset: 2px;
    transition: color 0.2s ease;

    &:hover {
      color: var(--btn-color-primary);
    }
  }

  &__close-btn {
    // padding: var(--spacing-xxs);
    // background: transparent;
    // border: none;
    display: flex;
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-sm);
    padding: var(--spacing-xxs);
    background: var(--color-secondary);
    color: var(--text-color-slate-gray);
    cursor: pointer;
    transition: color 0.2s ease, background 0.2s ease, border-color 0.2s ease;

    &:hover {
      color: var(--color-primary);
      background: var(--color-primary-opacity);
      border-color: var(--color-primary);
    }
  }

  &__content {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-semibold);
    color: var(--text-color-black);
    font-family: var(--font-family-primary);
    margin: 0;
  }

  &__options {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xsm);
  }

  &__option-btn {
    width: 100%;
    text-align: left;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    background-color: var(--color-secondary);
    color: var(--text-color-slate-gray);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--text-color-primary);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--text-color-white);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }

  // Range Section
  &__range-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  &__range-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
  }

  &__range-container {
    position: relative;
    height: 8px;
  }

  &__range-slider {
    position: absolute;
    width: 100%;
    height: 8px;
    background-color: var(--color-light-gray);
    border-radius: var(--border-radius-sm);
    appearance: none;
    cursor: pointer;
    outline: none;
    transition: all 0.2s ease;

    &::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: var(--color-primary);
      cursor: pointer;
      border: 2px solid var(--color-white);
      box-shadow: var(--shadow-sm);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    &::-moz-range-thumb {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: var(--color-primary);
      cursor: pointer;
      border: 2px solid var(--color-white);
      box-shadow: var(--shadow-sm);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
    }

    &:hover {
      &::-webkit-slider-thumb {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
      
      &::-moz-range-thumb {
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    }

    &--overlay {
      background: transparent;
      top: 0;
    }
  }

  &__range-inputs {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__range-input {
    width: 80px;
    padding: var(--field-padding);
    border: var(--field-border);
    border-radius: var(--field-radius);
    font-size: var(--font-size-sm);
    background-color: var(--color-white);
    color: var(--text-color-black);
    transition: border-color 0.2s ease, box-shadow 0.2s ease;

    &:hover {
      border-color: var(--color-primary);
    }

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  &__range-separator {
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
  }

  // Chips Section
  &__chips {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  &__chip {
    padding: var(--spacing-xxs) var(--spacing-xsm);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    text-transform: capitalize;
    background-color: var(--color-white);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
    border: 1px solid var(--border-color-light-gray);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      transform: scale(1.05);
    }

    &--allergen-active {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);

      &:hover {
        background-color: var(--color-primary-opacity);
        color: var(--color-primary);
      }
    }

    &--dietary-active {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);

      &:hover {
        background-color: var(--color-primary-opacity);
        color: var(--color-primary);
      }
    }

    &--loading {
      pointer-events: none;
      background-color: #f5f5f5;
      border-color: #e0e0e0;

      .skeleton-text {
        width: 60px;
        height: 14px;
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 2px;
      }
    }
  }

  &__loading {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
  }

  &__no-data {
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
    font-style: italic;
    padding: var(--spacing-sm);
  }

  &__footer {
    padding: var(--spacing-md);
    border-top: 1px solid var(--border-color-light-gray);

    @media (min-width: 768px) {
      display: none;
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
