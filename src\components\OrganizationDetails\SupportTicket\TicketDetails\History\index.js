'use client';
import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { EmptyHistorySVG } from '@/helper/common/images';
import TimeHistory from './TimeHistory';
import AttachmentHistory from './AttachmentHistory';
import CustomSelect from '@/components/UI/CustomSelect';
import './history.scss';

export default function History() {
  const dummyHistory = [
    {
      name: '<PERSON>',
      date: new Date('2024-12-05T10:05:00'),
      timeSpent: 3 * 60 + 38,
    },
    {
      name: '<PERSON>',
      date: new Date('2024-12-06T12:15:00'),
      timeSpent: 2 * 60 + 30,
    },
    {
      name: '<PERSON>',
      date: new Date('2024-12-07T14:45:00'),
      timeSpent: 4 * 60 + 15,
    },
    {
      name: '<PERSON>',
      date: new Date('2024-12-08T09:00:00'),
      timeSpent: 5 * 60 + 45,
    },
  ];

  const attachmentHistory = [
    {
      name: '<PERSON>',
      date: new Date('2024-12-05T10:11:00'),
      fileStatus: 'File deleted',
      fileName:
        'canva-letter-c-trade-marketing-logo-design-template-r9VFYrbB35Y.jpg',
      attachmentType: 'Private',
    },
    {
      name: 'Jane Doe',
      date: new Date('2024-12-06T12:11:00'),
      fileStatus: 'File edited',
      fileName: 'marketing-report-final.pdf',
      attachmentType: 'Public',
    },
    {
      name: 'Mark Smith',
      date: new Date('2024-12-07T14:11:00'),
      fileStatus: 'File attached',
      fileName: 'project-proposal.docx',
      attachmentType: 'Private',
    },
    {
      name: 'Emily Clark',
      date: new Date('2024-12-08T09:11:00'),
      fileStatus: 'File attached',
      fileName: 'presentation.pptx',
      attachmentType: 'Public',
    },
  ];

  const assigneeOptions = [
    { label: 'None', value: 'none' },
    { label: 'Time Entries', value: 'time entries' },
    { label: 'Attachment', value: 'attachment' },
  ];

  const historyFilterOptions = [
    { label: 'All History', value: 'all' },
    { label: 'My History', value: 'my' },
  ];

  const [filter, setFilter] = useState('all');
  const [assignee, setAssignee] = useState('none');

  // Handle "Time history" filter change
  const handleFilterChange = (selectedOption) => {
    const selectedFilter = selectedOption?.value || 'all';
    setFilter(selectedFilter);
  };

  // Handle "Filter by" selection change
  const handleAssigneeChange = (selectedOption) => {
    const selectedValue = selectedOption?.value || 'none';
    setAssignee(selectedValue);
  };

  // Render content based on assignee selection
  const renderContent = () => {
    if (assignee === 'none') {
      return (
        <Box className="d-flex justify-center align-center flex-col pt16 pb32">
          <EmptyHistorySVG />
          <Typography className="no-history-wrap body-sm">
            No history entries found
          </Typography>
        </Box>
      );
    } else if (assignee === 'time entries') {
      return <TimeHistory timeHistory={dummyHistory} />;
    } else {
      return <AttachmentHistory attachmentData={attachmentHistory} />;
    }
  };

  return (
    <Box className="history-wrap">
      <Box className="d-flex align-center justify-space-between">
        <Box className="d-flex align-center ticket-history-filter gap-sm">
          <label className="ticket-history-label body-sm">
            Ticket history :
          </label>
          <CustomSelect
            placeholder="Select"
            className="selected-wrap filter-select"
            options={historyFilterOptions}
            value={
              historyFilterOptions.find((opt) => opt?.value === filter) || ''
            }
            name="history-filter"
            onChange={(selectedOption) => handleFilterChange(selectedOption)}
          />
        </Box>

        <Box className="d-flex align-center gap-sm">
          <label className="ticket-history-label body-sm">Filter by :</label>
          <CustomSelect
            placeholder="Select"
            className="selected-wrap filter-by-select"
            options={assigneeOptions}
            value={assigneeOptions.find((opt) => opt?.value === assignee) || ''}
            name="assignee"
            onChange={(selectedOption) => handleAssigneeChange(selectedOption)}
          />
        </Box>
      </Box>

      {renderContent()}
    </Box>
  );
}
