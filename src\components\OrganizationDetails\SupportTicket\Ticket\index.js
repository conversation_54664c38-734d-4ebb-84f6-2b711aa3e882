import { Box, Tooltip, Typography } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import React from 'react';
import CustomSelect from '@/components/UI/CustomSelect';
import { DateFormat } from '@/helper/common/commonFunctions';
import './ticket.scss';

const statusOptions = [
  { label: 'Open', value: 'open' },
  { label: 'Escalated', value: 'escalated' },
  { label: 'In-Progress', value: 'in_progress' },
  { label: 'Invoiced', value: 'invoiced' },
  { label: 'On Hold', value: 'on_hold' },
  { label: 'QA Review', value: 'qa_review' },
  { label: 'Assigned', value: 'assigned' },
  { label: 'Under Review', value: 'under_review' },
  { label: 'Closed', value: 'closed' },
];

export default function Ticket({
  selectedStatus,
  setSelectedStatus,
  onTicketClick,
  ticketsList = [], // Accept list of tickets
  selectedTicket, // Track which ticket is selected
  hideDropdown = false, // New prop to conditionally hide dropdown
  hideStatusChip = false, // New prop to conditionally hide status chip
  showBottomPadding = false, // New prop to conditionally show pb8 class
}) {
  // Function to get priority CSS class
  const getPriorityClass = (priority) => {
    const priorityMap = {
      urgent: 'failed',
      high: 'failed',
      medium: 'draft',
      low: 'active-onboarding',
      none: 'draft',
      HIGH: 'failed',
      MEDIUM: 'draft',
      LOW: 'active-onboarding',
      URGENT: 'failed',
      NONE: 'draft',
    };
    return priorityMap[priority?.toLowerCase()] || 'draft';
  };

  // Function to format priority display consistently
  const formatPriorityDisplay = (priority) => {
    if (!priority) return '';
    // Convert to lowercase first, then capitalize first letter
    const formatted = priority.toLowerCase();
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
  };

  // Function to get status CSS class
  const getStatusClass = (status) => {
    const statusMap = {
      open: 'status-yellow',
      escalated: 'failed',
      in_progress: 'draft',
      invoiced: 'success',
      on_hold: 'status-yellow',
      qa_review: 'draft',
      assigned: 'draft',
      under_review: 'draft',
      resolved: 'success',
      closed: 'success',
    };
    return statusMap[status] || 'draft';
  };
  // Use only provided tickets list - no default fallback to avoid duplication
  const ticketsToDisplay = ticketsList;

  const handleTicketClick = (ticket) => (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onTicketClick) {
      onTicketClick(ticket);
    }
  };

  const handleStatusChange = (ticket, newStatus) => {
    if (setSelectedStatus) {
      setSelectedStatus(newStatus);
    }
    // In a real app, you would also update the ticket's status in the database
  };

  // Don't render anything if no tickets provided
  if (!ticketsToDisplay || ticketsToDisplay.length === 0) {
    return null;
  }

  return (
    <Box className="tickets-list-container ticket-list-container-wrap">
      {ticketsToDisplay.map((ticketData, index) => (
        <Box
          key={ticketData.id || index}
          className={`ticket-wrap ${selectedTicket?.id === ticketData.id ? 'selected' : ''}`}
          onClick={handleTicketClick(ticketData)}
          style={{ cursor: 'pointer', marginBottom: '16px' }}
        >
          <Box className="heading-wrap d-flex align-center justify-space-between gap-sm pb4 flex-wrap">
            <Box className="d-flex align-center gap-sm">
              <Typography className="heading-text-wrap body-sm d-flex align-center gap-sm">
                <span>#{ticketData.id}</span> {ticketData.subject}
              </Typography>
              {ticketData.status &&
                !hideStatusChip &&
                (hideDropdown ||
                  (ticketData.status !== 'open' &&
                    ticketData.status !== 'in_progress')) && (
                  <Typography
                    className={`sub-title-text fw600 ${getStatusClass(ticketData.status)}`}
                  >
                    {ticketData.status.replace('_', ' ')}
                  </Typography>
                )}
            </Box>
            <Box className="d-flex align-center gap-md">
              {ticketData.priority && (
                <Typography
                  className={`sub-title-text fw600 ${getPriorityClass(ticketData.priority)}`}
                >
                  {formatPriorityDisplay(ticketData.priority)}
                </Typography>
              )}
            </Box>
          </Box>

          <Box className="description-wrap">
            <Tooltip
              title={
                <Typography className="sub-title-text">
                  {ticketData?.description}
                </Typography>
              }
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Typography className="description-text body-sm">
                {ticketData.description
                  ? ticketData.description.length > 30
                    ? `${ticketData.description.substring(0, 30)}...`
                    : ticketData.description
                  : 'No description available'}
              </Typography>
            </Tooltip>
          </Box>

          <Box className="name-time-wrap pb4 pt4">
            <Box
              className={`d-flex align-center gap-5 ${showBottomPadding ? 'pb8' : ''}`}
            >
              <PersonIcon className="user-icon" />
              <Typography className="name-text body-sm">
                {ticketData.name}
              </Typography>
            </Box>
            <Box className="d-flex align-center gap-5">
              <AccessTimeIcon className="time-icon" />
              <Typography className="time-text body-sm">
                {ticketData.createdAt &&
                  DateFormat(ticketData.createdAt, 'datesWithhour')}
              </Typography>
            </Box>
          </Box>

          {/* {ticketData.assignedTo && (
            <Box className="assigned-wrap">
              <p className="assigned-text body-sm">
                <span className="assigned-label">Assigned to:</span>{' '}
                {ticketData.assignedTo}
              </p>
            </Box>
          )} */}

          {!hideDropdown && (
            <Box
              className=""
              onClick={(e) => {
                e.stopPropagation(); // Prevent ticket click when clicking on select
              }}
            >
              <CustomSelect
                className="slected-wrap ticket-status-select"
                placeholder="Select Status"
                options={statusOptions}
                value={
                  statusOptions?.find(
                    (opt) =>
                      opt?.value === (ticketData.status || selectedStatus)
                  ) || ''
                }
                name="status"
                onChange={(selectedOption) =>
                  handleStatusChange(ticketData, selectedOption?.value || '')
                }
              />
            </Box>
          )}

          {/* <Box className="profile-wrap">
            <Image
              className="profile-image"
              src={ProfileImage}
              alt="Profile Image"
              width={100}
              height={100}
            />
          </Box> */}
        </Box>
      ))}
    </Box>
  );
}
