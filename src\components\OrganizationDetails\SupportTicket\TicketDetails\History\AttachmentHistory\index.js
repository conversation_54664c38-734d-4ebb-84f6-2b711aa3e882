import React from 'react';
import { Box, Divider, Typography } from '@mui/material';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import CircleIcon from '@mui/icons-material/Circle';
import { DateFormat } from '@/helper/common/commonFunctions';
import './attachmenthistory.scss';

const AttachmentHistory = ({ attachmentData }) => {
  return (
    <Box className="attachment-wrap">
      <Box className="devider-wrap">
        <Divider orientation="vertical" className="vertical-divider" />
      </Box>
      <Box className="attachment-history-wrap">
        {attachmentData.map((entry, index) => (
          <Box key={index} className="d-flex attachment-history">
            <Box className="devider-wrap">
              <Divider orientation="vertical" className="vertical-divider" />
            </Box>
            <Box className="attachment-item">
              <Box className="d-flex align-center header-date-wrap">
                <CalendarMonthIcon className="calender-icon" />
                <Typography component="p" className="header-date">
                  {DateFormat(entry.date, 'dates')}
                </Typography>
              </Box>
              <Typography component="p" className="time-wrap">
                {DateFormat(entry.date, 'hoursUTC')}
              </Typography>
              <Box className="d-flex align-center name-text-wrap">
                <CircleIcon className="circle-wrap" />
                <Typography className="file-status-wrap">
                  {entry.fileStatus}
                </Typography>
              </Box>
              <Typography className="file-name">
                <span className="file-name-text">File Name </span>
                {entry.fileName}
              </Typography>
              <Typography className="attachment-type-wrap">
                <span className="attachment-type-text">Attachment Type </span>
                {entry.attachmentType}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default AttachmentHistory;
