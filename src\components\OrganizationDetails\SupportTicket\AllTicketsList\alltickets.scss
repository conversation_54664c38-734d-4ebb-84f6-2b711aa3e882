.all-tickets-container-wrap {
  max-height: calc(100vh - 135px);
  overflow-y: auto;

  .all-tickets-container {
    // Simple fix: Add max-height for scroll functionality
    .all-tickets-header {
      margin-bottom: var(--spacing-xl);
    }

    .tickets-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
      gap: var(--spacing-lg);
      width: 100%;

      @media (max-width: 1199px) {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: var(--spacing-md);
      }

      @media (max-width: 899px) {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
      }

      .ticket-card-wrapper {
        width: 100%;

        // Ensure ticket cards take full width of grid cell
        .all-tickets-list-container {
          width: 100%;

          .ticket-wrap {
            width: 100%;
            margin-bottom: 0;

            &.selected {
              box-shadow: 0 4px 12px rgba(19, 94, 150, 0.2);
            }

            // Override name-time layout for All Tickets page
            .name-time-wrap {
              display: flex;
              align-items: center;
              gap: var(--spacing-sm);
              width: 100%;

              .name-wrap {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
                padding-bottom: 0;
              }

              .time-wrap {
                display: flex;
                align-items: center;
                gap: var(--spacing-xs);
              }
            }
          }
        }
      }
    }
  }
}
// Responsive adjustments for ticket cards in grid
@media (max-width: 1499px) {
  .all-tickets-container .tickets-grid .ticket-card-wrapper {
    .all-tickets-list-container .ticket-wrap {
      padding: var(--spacing-md);

      .heading-text-wrap {
        font-size: var(--font-size-sm);
      }

      .description-text {
        font-size: var(--font-size-xs);
      }
    }
  }
}

@media (max-width: 899px) {
  .all-tickets-container .tickets-grid .ticket-card-wrapper {
    .all-tickets-list-container .ticket-wrap {
      padding: var(--spacing-sm);

      .name-time-wrap {
        .user-icon,
        .time-icon {
          font-size: var(--font-size-sm);
        }

        .name-text,
        .time-text {
          font-size: var(--font-size-xs);
        }
      }
    }
  }
}
