import { Box, Typography } from '@mui/material';
import React from 'react';
import './nodata.scss';

export default function NoDataView({
  image,
  title,
  description,
  className = '',
}) {
  return (
    <Box
      className={`no-data-wrap d-flex flex-col align-center justify-center ${className}`}
    >
      {image && (
        <Box className="no-data-image ">
          <img src={image} alt="No Data" />
        </Box>
      )}
      {title && (
        <Typography variant="h6" className="no-data-title title-sm">
          {title}
        </Typography>
      )}
      {description && (
        <Typography
          variant="body2"
          className="no-data-description body-sm-regular"
        >
          {description}
        </Typography>
      )}
    </Box>
  );
}
