'use client';
import { useState, useContext } from 'react';
import { useDropzone } from 'react-dropzone';
import { Box, Tooltip, Typography } from '@mui/material';
import CustomButton from '@/components/UI/CustomButton';
import { EmptAttachmentIcon } from '@/helper/common/images';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import RemoveRedEyeOutlinedIcon from '@mui/icons-material/RemoveRedEyeOutlined';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import PermMediaOutlinedIcon from '@mui/icons-material/PermMediaOutlined';
import PreviewModal from '../../CreateTicket/PreviewModal';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import AuthContext from '@/helper/authcontext';
import './attachment.scss';
import Pohover from '../../Popover';

export default function Attachment() {
  const [mediaFiles, setMediaFiles] = useState([]);
  const [previewMedia, setPreviewMedia] = useState(null);
  const [openModal, setOpenModal] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const { planDetail, authState, setRestrictedLimitModal } =
    useContext(AuthContext);

  const handlePopoverOpen = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handlePopoverClose = () => {
    setAnchorEl(null);
  };

  const handlePreviewClick = (preview) => {
    if (preview?.type === 'image') {
      setPreviewMedia(preview?.preview);
    } else {
      setPreviewMedia(preview);
    }
    setOpenModal(true);
  };

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop: (acceptedFiles) => {
      // Check if storage is full
      const totalStorage = planDetail?.total_storage || 0;
      const usedStorage = authState?.subscriptionUsage?.total_size_gb || 0;
      const totalNewFileSizeInGB = acceptedFiles.reduce((total, file) => {
        return total + file.size / (1024 * 1024 * 1024); // Convert bytes to GB
      }, 0);

      if (usedStorage + totalNewFileSizeInGB > totalStorage) {
        setRestrictedLimitModal({
          storageLimit: true,
          totalStorage: planDetail?.total_storage,
          usedStorage: authState?.subscriptionUsage?.total_size_gb,
        });
        return;
      }

      if (acceptedFiles.length) {
        const newMedia = acceptedFiles?.map((file) => ({
          file,
          name: file?.name,
          preview: URL.createObjectURL(file),
          type: file?.type.split('/')[0],
        }));
        setMediaFiles((prevFiles) => [...prevFiles, ...newMedia]);
      }
    },
    accept: 'image/*,video/*',
    multiple: true,
    noClick: true,
  });

  const removeMedia = (index) => {
    setMediaFiles((prevFiles) => prevFiles?.filter((_, i) => i !== index));
  };

  const handleDownload = (fileUrl, fileName) => {
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;
    link.click();
  };

  return (
    <Box className="attachment-wrap d-flex flex-col align-center justify-center text-align">
      {mediaFiles.length === 0 ? (
        <Box className="empty-attachment">
          <EmptAttachmentIcon />
          <Box className="attachment-text-wrap">
            <Typography className="no-attachment body-sm">
              No Attachments available
            </Typography>
            <Typography className="upload-attachment body-sm">
              Upload attachments to add more context to this Ticket.
            </Typography>
          </Box>

          <Box className="browse-files-wrap">
            <CustomButton
              onClick={open}
              variant="contained"
              title="Browse files"
            />
          </Box>
        </Box>
      ) : (
        <Box className="media-previews w100">
          <Box className="add-file-wrap d-flex justify-end">
            <CustomButton
              className="p16 add-file"
              onClick={open}
              fontWeight="600"
              variant="contained"
              background="#39596e"
              backgroundhover="#39596e"
              colorhover="#FFFFFF"
              title="Browse files"
            />
          </Box>
          {mediaFiles?.map((media, index) => (
            <Box
              key={index}
              className="preview-container d-flex align-center justify-space-between pl32 pr32 "
            >
              <Box className="file-name-wrap d-flex  align-center gap-30">
                <Box className="media-icon-wrap">
                  <PermMediaOutlinedIcon
                    sx={{ cursor: 'pointer' }}
                    className="icon-wrap"
                  />
                </Box>
                <Tooltip title={media?.name} arrow>
                  <Typography className="file-name body-sm">
                    {media?.name}
                  </Typography>
                </Tooltip>
              </Box>
              <Box className="more-item-icon">
                <MoreHorizIcon
                  onClick={handlePopoverOpen}
                  sx={{ cursor: 'pointer' }}
                  className="more-item"
                />
                <Pohover
                  anchorEl={anchorEl}
                  setAnchorEl={setAnchorEl}
                  handlePopoverClose={handlePopoverClose}
                  handlePreviewClick={handlePreviewClick}
                  media={media}
                  handleDownload={handleDownload}
                  removeMedia={removeMedia}
                  index={index}
                />
              </Box>
              <Box className="icons-wrap d-flex gap-sm">
                <Tooltip title="Preview" arrow>
                  <RemoveRedEyeOutlinedIcon
                    onClick={() => handlePreviewClick(media)}
                    className="eye-icon"
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>
                <Tooltip title="Download" arrow>
                  <FileDownloadOutlinedIcon
                    onClick={() => handleDownload(media?.preview, media?.name)}
                    className="download-icon"
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>

                <Tooltip title="Delete" arrow>
                  <DeleteOutlineIcon
                    className="delete-icon"
                    onClick={() => removeMedia(index)}
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>
              </Box>
            </Box>
          ))}
        </Box>
      )}
      <Box {...getRootProps()} style={{ display: 'none' }}>
        <input {...getInputProps()} />
      </Box>
      <PreviewModal
        open={openModal}
        setOpen={setOpenModal}
        previewMedia={previewMedia}
      />
    </Box>
  );
}
