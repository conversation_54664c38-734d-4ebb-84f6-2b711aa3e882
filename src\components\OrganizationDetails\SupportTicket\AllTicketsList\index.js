'use client';

import React, { useState, useMemo } from 'react';
import { Box, Typography, Tooltip, Divider } from '@mui/material';
import { useTheme, useMediaQuery } from '@mui/material';
import { useRouter } from 'next/navigation';
import Ticket from '../Ticket';
import CustomSearch from '@/components/UI/CustomSearch';
import FilterListIcon from '@mui/icons-material/FilterList';
import CheckIcon from '@mui/icons-material/Check';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomButton from '@/components/UI/CustomButton';
import './alltickets.scss';

export default function AllTicketsList() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const [searchValue, setSearchValue] = useState('');
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const router = useRouter();

  // Filter related state
  const [selectedFilters, setSelectedFilters] = useState([
    'search',
    'status',
    'priority',
    'dateRange',
  ]);
  const [filterData, setFilterData] = useState({
    status: '',
    priority: '',
    dateRange: '',
  });

  // Sample tickets data - replace with real data from API
  const allTicketsData = [
    {
      id: 100,
      subject: 'Cannot login to account',
      description: 'User unable to access their account after password reset',
      name: 'Emily Davis',
      userName: 'Emily Davis',
      organizationName: 'TechCorp',
      createdAt: new Date('2024-01-15T12:30:00'),
      status: 'in_progress',
      priority: 'HIGH',
      category: 'Account Issues',
      assignedTo: 'Sarah Johnson',
    },
    {
      id: 101,
      subject: 'Feature request: Dark mode',
      description: 'Would love to have a dark mode option for better usability',
      name: 'Robert Brown',
      userName: 'Robert Brown',
      organizationName: 'DesignStudio',
      createdAt: new Date('2024-01-14T14:01:00'),
      status: 'open',
      priority: 'LOW',
      category: 'Feature Request',
      assignedTo: 'Mike Wilson',
    },
    {
      id: 102,
      subject: 'Payment processing error',
      description: 'Getting error 500 when trying to process payment',
      name: 'Emily Davis',
      userName: 'Emily Davis',
      organizationName: 'TechCorp',
      createdAt: new Date('2024-01-13T13:01:00'),
      status: 'resolved',
      priority: 'URGENT',
      category: 'Billing',
      assignedTo: 'Mike Wilson',
    },
    {
      id: 103,
      subject: 'Slow page loading',
      description: 'Dashboard takes a very long time to load',
      name: 'Emily Davis',
      userName: 'Emily Davis',
      organizationName: 'TechCorp',
      createdAt: new Date('2024-01-12T12:01:00'),
      status: 'open',
      priority: 'MEDIUM',
      category: 'Performance',
      assignedTo: 'Sarah Johnson',
    },
  ];

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: [
          { label: 'Open', value: 'open' },
          { label: 'Escalated', value: 'escalated' },
          { label: 'In Progress', value: 'in_progress' },
          { label: 'Invoiced', value: 'invoiced' },
          { label: 'On Hold', value: 'on_hold' },
          { label: 'QA Review', value: 'qa_review' },
          { label: 'Assigned', value: 'assigned' },
          { label: 'Under Review', value: 'under_review' },
          { label: 'Resolved', value: 'resolved' },
          { label: 'Closed', value: 'closed' },
        ],
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: [
          { label: 'Low', value: 'LOW' },
          { label: 'Medium', value: 'MEDIUM' },
          { label: 'High', value: 'HIGH' },
          { label: 'Urgent', value: 'URGENT' },
        ],
        permission: true,
      },
      {
        key: 'dateRange',
        label: 'Date Range',
        options: [
          { label: 'Today', value: 'today' },
          { label: 'Yesterday', value: 'yesterday' },
          { label: 'Last 7 Days', value: 'last_7_days' },
          { label: 'Last 30 Days', value: 'last_30_days' },
          { label: 'Last 90 Days', value: 'last_90_days' },
          { label: 'This Month', value: 'this_month' },
          { label: 'Last Month', value: 'last_month' },
        ],
        permission: true,
      },
    ],
    []
  );

  const handleTicketClick = (ticket) => {
    // Navigate to main support ticket page with ticket ID as query parameter
    router.push(`/support-ticket?id=${ticket.id}`);
  };

  // Filter functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) =>
      prev.includes(filterKey)
        ? prev.filter((key) => key !== filterKey)
        : [...prev, filterKey]
    );
  };

  const saveLayout = () => {
    // Implementation for saving filter layout
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      saveLayout();
    }
  };

  const handleApplyFilter = () => {
    setOpenFilterDrawer(false);
    // Apply filter logic here - filter tickets based on filterData
    // In real implementation, this would trigger API call with filters
  };

  const handleClearFilter = () => {
    setFilterData({
      status: '',
      priority: '',
      dateRange: '',
    });
    setSearchValue('');
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title d-flex align-center gap-sm">
              <Typography className="sub-header-text">
                All Support Tickets
              </Typography>
            </Box>

            {/* Search and Filter Section - Same as Support Ticket */}
            <Box className="mr8 pr4 d-flex align-center gap-sm">
              <Box className="search-section-wrap">
                {!isMobile &&
                  selectedFilters?.map((key) => {
                    const filter = filters?.find((f) => f?.key === key);
                    return filter?.permission ? (
                      <React.Fragment key={key}>
                        {key === 'search' ? (
                          <Box className="search-section-fields">
                            <CustomSearch
                              fullWidth
                              setSearchValue={setSearchValue}
                              onKeyPress={handleKeyPress}
                              searchValue={searchValue}
                            />
                          </Box>
                        ) : (
                          <Box className="search-section-fields">
                            <CustomSelect
                              placeholder={filter?.label}
                              options={filter?.options}
                              value={
                                filter?.options?.find((opt) => {
                                  return opt?.value === filterData[key];
                                }) || ''
                              }
                              onChange={(e) =>
                                setFilterData({
                                  ...filterData,
                                  [key]: e?.value,
                                })
                              }
                              menuPortalTarget={document.body}
                              styles={{
                                menuPortal: (base) => ({
                                  ...base,
                                  zIndex: 9999,
                                }),
                              }}
                            />
                          </Box>
                        )}
                      </React.Fragment>
                    ) : null;
                  })}

                {!isMobile && (
                  <>
                    <Box>
                      <CustomButton
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Apply Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <CheckIcon />
                          </Tooltip>
                        }
                        onClick={handleApplyFilter}
                      />
                    </Box>
                    <Box>
                      <CustomButton
                        variant="outlined"
                        isIconOnly
                        startIcon={
                          <Tooltip
                            title={
                              <Typography className="sub-title-text">
                                Clear Filter
                              </Typography>
                            }
                            arrow
                            classes={{
                              tooltip: 'info-tooltip-container',
                            }}
                          >
                            <ClearOutlinedIcon />
                          </Tooltip>
                        }
                        onClick={handleClearFilter}
                      />
                    </Box>
                  </>
                )}
              </Box>
              <CustomButton
                isIconOnly
                startIcon={
                  <Tooltip
                    title={
                      <Typography className="sub-title-text">
                        Filters
                      </Typography>
                    }
                    classes={{ tooltip: 'info-tooltip-container' }}
                    arrow
                  >
                    <FilterListIcon />
                  </Tooltip>
                }
                onClick={() => {
                  setOpenFilterDrawer(true);
                }}
              />
            </Box>
          </Box>
          <Divider />
        </Box>

        <Box className="section-right-content">
          <Box className="all-tickets-container-wrap">
            <Box className="all-tickets-container">
              {/* Tickets List */}
              <Box className="tickets-grid">
                {allTicketsData.map((ticket) => (
                  <Box key={ticket.id} className="ticket-card-wrapper">
                    <Box className="all-tickets-list-container">
                      <Ticket
                        ticketsList={[ticket]}
                        selectedTicket={null}
                        onTicketClick={handleTicketClick}
                        hideDropdown={true}
                      />
                    </Box>
                  </Box>
                ))}
              </Box>

              {/* Filter Drawer */}
              <Box className="drawer-wrap">
                <RightDrawer
                  className="filter-options-drawer"
                  anchor="right"
                  open={openFilterDrawer}
                  onClose={() => setOpenFilterDrawer(false)}
                  title="Filter"
                  content={
                    <Box>
                      <FilterComponent
                        filters={filters}
                        filterData={filterData}
                        setFilterData={setFilterData}
                        selectedFilters={selectedFilters}
                        toggleFilter={toggleFilter}
                        saveLayout={saveLayout}
                        setOpenFilterDrawer={setOpenFilterDrawer}
                        setSelectedFilters={setSelectedFilters}
                        getFirstFourFilters={getFirstFourFilters}
                        setSearchValue={setSearchValue}
                        searchValue={searchValue}
                        handleKeyPress={handleKeyPress}
                        isMobile={isMobile}
                        handleApplyFilter={handleApplyFilter}
                        handleClearFilter={handleClearFilter}
                      />
                    </Box>
                  }
                />
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
}
