.recipe-history-main-container {
  height: 100%;

  .recipe-history-container {
    height: 100%;
    overflow: auto;
    padding: var(--spacing-xxl);
    width: 100%;
  }

  .right-sidebar-container {
    max-width: 320px;
    width: 100%;
    height: 100%;
    border-left: var(--normal-sec-border);
    padding: var(--spacing-xxl) 0;
    .recipe-sidebar-header-container {
      border-bottom: var(--normal-sec-border);
      padding-bottom: var(--spacing-lg);
    }
    .recipe-sidebar-header {
      padding: 0 var(--spacing-xxl);
    }
  }

  .version-title-header {
    margin-bottom: 16px;
  }

  .recipe-history-btn-list {
    .recipe-history-btn {
      padding: var(--spacing-sm);
      border-radius: var(--border-radius-xs);
      cursor: pointer;
    }
  }

  .recipe-history-content {
    border: var(--normal-sec-border);
    border-radius: var(--border-radius-xs);
    margin: 20px 5px;
    padding: var(--spacing-lg);
    font-family: var(--font-family-primary);
  }

  .history-log {
    padding: 16px var(--spacing-xxl);

    &:hover {
      background-color: var(--color-primary-opacity);
    }

    &.active {
      background-color: var(--color-primary-opacity);
    }

    .history-log-remark {
      color: var(--text-color-slate-gray);
      display: -webkit-box;
      -webkit-line-clamp: 1;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }
}
