'use client';

import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import Link from 'next/link';
import NoDataView from '@/components/UI/NoDataView';
import './quickactionpanel.scss';

const QuickActionPanel = ({ actions = [] }) => {
  const getActionColorClass = (color) => {
    return `quick-action-panel__action--${color}`;
  };

  return (
    <div className="quick-action-panel">
      {/* Panel Header */}
      <div className="quick-action-panel__header">
        <div className="quick-action-panel__header-content">
          <div className="quick-action-panel__header-info">
            <h3 className="sub-header-text">Quick Actions</h3>
            <p className="quick-action-panel__subtitle">
              Quick access to recipe management sections
            </p>
          </div>
          <Icon
            name="Zap"
            size={20}
            className="quick-action-panel__header-icon"
          />
        </div>
      </div>

      {/* Actions Grid */}
      <div className="quick-action-panel__content">
        {actions && actions.length > 0 ? (
          <div className="quick-action-panel__grid">
            {actions.map((action) => (
              <Link
                key={action.id}
                href={action.action || '#'}
                className={`quick-action-panel__action ${getActionColorClass(action.color)}`}
              >
                <div className="quick-action-panel__action-content">
                  <div className="quick-action-panel__action-main">
                    <div className="quick-action-panel__action-icon">
                      {/* <Icon name={action.icon} size={20} /> */}
                      {action?.icon}
                    </div>
                    <div className="quick-action-panel__action-info">
                      <h4 className="quick-action-panel__action-title">
                        {action?.title}
                      </h4>
                    </div>
                  </div>
                  {/* <div className="quick-action-panel__action-footer">
                    <Icon
                      name="ArrowRight"
                      size={16}
                      className="quick-action-panel__action-arrow"
                    />
                  </div> */}
                </div>
              </Link>
            ))}
          </div>
        ) : (
          <NoDataView
            title="No Quick Actions Available"
            description="There are no quick actions available at the moment."
            className="no-data-auto-margin-height-conainer"
          />
        )}
      </div>
    </div>
  );
};

export default QuickActionPanel;
