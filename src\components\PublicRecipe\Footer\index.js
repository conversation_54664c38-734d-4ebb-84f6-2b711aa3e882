import { Box, Typography } from '@mui/material';
import { INFO_LINKS } from '@/helper/constants/urls';
import Link from 'next/link';
import Image from 'next/image';
import HelpText from './components/HelpText';
import MessageButton from './components/MessageButton';
import poweredByImage from '../../../../public/images/app-logo.svg';
import './footer.scss';

export default function PublicRecipeFooter() {
  const footerLinks = [
    { text: 'Contact us', path: INFO_LINKS?.contactUs },
    { text: 'Privacy notice', path: INFO_LINKS?.PrivacyPolicy },
    { text: 'Terms and conditions', path: INFO_LINKS?.TermsConditions },
  ];

  return (
    <footer className="public-footer-section">
      <Box className="footer-content-wrap">
        <HelpText />
        <MessageButton />
      </Box>
      <Box className="footer-links d-flex justify-center gap-4">
        {footerLinks?.map((link, index) => (
          <Link key={index} href={link?.path} target="_blank">
            <Typography className="text-decoration-none brown-text footer-link-text">
              {link?.text}
            </Typography>
          </Link>
        ))}
      </Box>
      <Box className="powered-by-wrap text-align d-flex align-center justify-center">
        <Typography className="brown-content-small-text">Powered By</Typography>
        <Image src={poweredByImage} alt="Powered By" width={50} height={30} />
      </Box>
    </footer>
  );
}
