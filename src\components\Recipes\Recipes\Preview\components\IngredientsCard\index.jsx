import React, { useContext } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import { getCurrencySymbol } from '@/helper/common/commonFunctions';
import AuthContext from '@/helper/authcontext';
import NoDataView from '@/components/UI/NoDataView';
import './IngredientsCard.scss';

const IngredientsCard = ({
  ingredients,
  isPublicPage = false,
  highlightData = null,
  scaling,
}) => {
  const { authState } = useContext(AuthContext);
  const currency = getCurrencySymbol(authState?.currency_details);

  // Check if ingredients are highlighted
  const isIngredientsHighlighted = highlightData?.ingredients;

  // Helper function to render ingredient name with highlighting
  const renderIngredientNameWithHighlight = (ingredientName) => {
    if (isIngredientsHighlighted && highlightData?.ingredients) {
      // Find the corresponding old ingredient data
      const oldIngredient = highlightData?.ingredients?.find(
        (ing) => ing?.ingredient_name === ingredientName
      );
      if (oldIngredient && oldIngredient?.ingredient_name !== ingredientName) {
        return (
          <div className="highlight-container">
            <p className="ingredients-card__item-name highlight-no-margin-bottom">
              {ingredientName}
            </p>
            <p className="highlight-original-text">
              {oldIngredient?.ingredient_name}
            </p>
          </div>
        );
      }
    }

    // Normal display
    return <p className="ingredients-card__item-name">{ingredientName}</p>;
  };

  // Helper function to render ingredient cost with highlighting
  const renderIngredientCostWithHighlight = (finalCost, ingredientName) => {
    if (isIngredientsHighlighted && highlightData?.ingredients) {
      // Find the corresponding old ingredient data by ingredient name
      const oldIngredient = highlightData?.ingredients?.find(
        (ing) => ing?.ingredient_name === ingredientName
      );
      if (oldIngredient) {
        const oldWastageMultiplier =
          1 + (oldIngredient?.waste_percentage || 0) / 100;
        const oldBaseCost = oldIngredient?.ingredient_cost || 0;
        const oldScaledQuantity =
          (oldIngredient?.ingredient_quantity || 0) * (scaling || 1);
        const oldFinalCost =
          oldScaledQuantity * oldBaseCost * oldWastageMultiplier;

        if (oldFinalCost !== finalCost) {
          return (
            <div className="highlight-container-right">
              <p className="ingredients-card__item-cost highlight-no-margin-bottom">
                {currency}
                {finalCost?.toFixed(2)}
              </p>
              <p className="highlight-original-text old-ingredient-cost">
                {currency}
                {oldFinalCost?.toFixed(2)}
              </p>
            </div>
          );
        }
      }
    }

    // Normal display
    return (
      <p className="ingredients-card__item-cost">
        {currency}
        {finalCost?.toFixed(2)}
      </p>
    );
  };

  // Helper function to render quantity with highlighting
  const renderQuantityWithHighlight = (scaledQuantity, ingredientName) => {
    if (isIngredientsHighlighted && highlightData?.ingredients) {
      // Find the corresponding old ingredient data by ingredient name
      const oldIngredient = highlightData?.ingredients?.find(
        (ing) => ing?.ingredient_name === ingredientName
      );
      if (oldIngredient) {
        const oldScaledQuantity =
          (oldIngredient?.ingredient_quantity || 0) * (scaling || 1);
        if (oldScaledQuantity !== scaledQuantity) {
          return (
            <div className="highlight-container">
              <span className="ingredients-card__detail-value highlight-no-margin-bottom">
                {parseFloat(scaledQuantity?.toFixed(2))}
              </span>
              <span className="highlight-original-text old-ingredient-quantity">
                {parseFloat(oldScaledQuantity?.toFixed(2))}
              </span>
            </div>
          );
        }
      }
    }

    // Normal display
    return (
      <span className="ingredients-card__detail-value">
        {parseFloat(scaledQuantity?.toFixed(2))}
      </span>
    );
  };

  // Helper function to render wastage with highlighting
  const renderWastageWithHighlight = (wastage, ingredientName) => {
    if (isIngredientsHighlighted && highlightData?.ingredients) {
      // Find the corresponding old ingredient data by ingredient name
      const oldIngredient = highlightData?.ingredients?.find(
        (ing) => ing?.ingredient_name === ingredientName
      );
      if (oldIngredient && oldIngredient?.ingredient_wastage !== wastage) {
        return (
          <div className="highlight-container">
            <span className="ingredients-card__detail-value highlight-no-margin-bottom">
              {wastage}%
            </span>
            <span className="highlight-original-text old-ingredient-cost">
              {oldIngredient?.ingredient_wastage &&
                oldIngredient?.ingredient_wastage + '%'}
            </span>
          </div>
        );
      }
    }

    // Normal display
    return <span className="ingredients-card__detail-value">{wastage}%</span>;
  };

  // Helper function to render measure title with highlighting
  const renderMeasureTitleWithHighlight = (measureTitle, ingredientName) => {
    if (isIngredientsHighlighted && highlightData?.ingredients) {
      // Find the corresponding old ingredient data by ingredient name
      const oldIngredient = highlightData?.ingredients?.find(
        (ing) => ing?.ingredient_name === ingredientName
      );
      if (oldIngredient && oldIngredient?.measure_title !== measureTitle) {
        return (
          <div className="highlight-container">
            <span className="ingredients-card__measure-title highlight-no-margin-bottom">
              {measureTitle}
            </span>
            <span className="highlight-original-text old-ingredient-measure">
              {oldIngredient?.measure_title}
            </span>
          </div>
        );
      }
    }

    // Normal display
    return (
      <span className="ingredients-card__measure-title">{measureTitle}</span>
    );
  };

  return (
    <div className="ingredients-card">
      <div className="ingredients-card__header">
        <p className="ingredients-card__title">
          <Icon name="ShoppingCart" size={20} color="currentColor" />
          <span>Ingredients</span>
        </p>
      </div>
      <div className="ingredients-card__content">
        {!ingredients || ingredients?.length === 0 ? (
          <NoDataView
            title="No Ingredients Available"
            description="There are no ingredients available for this recipe at the moment."
            className="no-data-auto-height-conainer"
          />
        ) : (
          <div className="ingredients-card__list">
            {ingredients?.map((ingredient) => {
              const wastageMultiplier =
                1 + (ingredient?.waste_percentage || 0) / 100;
              const baseCost = ingredient?.ingredient_cost || 0;
              // Apply scaling to quantity and cost
              const scaledQuantity =
                (ingredient?.ingredient_quantity || 0) * (scaling || 1);
              const finalCost = scaledQuantity * baseCost * wastageMultiplier;

              return (
                <div key={ingredient?.id} className="ingredients-card__item">
                  <div className="ingredients-card__item-header">
                    {renderIngredientNameWithHighlight(
                      ingredient?.ingredient_name
                    )}
                    {/* Hide cost on public pages */}
                    {!isPublicPage &&
                      renderIngredientCostWithHighlight(
                        finalCost,
                        ingredient?.ingredient_name
                      )}
                  </div>

                  <div className="ingredients-card__item-details">
                    <div className="ingredients-card__detail">
                      <span className="ingredients-card__detail-label">
                        Quantity:
                      </span>
                      {renderQuantityWithHighlight(
                        scaledQuantity,
                        ingredient?.ingredient_name
                      )}
                      {renderMeasureTitleWithHighlight(
                        ingredient?.measure_title,
                        ingredient?.ingredient_name
                      )}
                    </div>
                    <div className="ingredients-card__detail">
                      <span className="ingredients-card__detail-label">
                        Wastage:
                      </span>
                      {renderWastageWithHighlight(
                        ingredient?.ingredient_wastage,
                        ingredient?.ingredient_name
                      )}
                    </div>
                  </div>

                  <div className="ingredients-card__item-methods">
                    {ingredient?.is_preparation_method === 1 && (
                      <div className="ingredients-card__method">
                        <span className="ingredients-card__method-label">
                          Prep:
                        </span>
                        <span className="ingredients-card__method-value">
                          {ingredient?.preparation_method_title ||
                            'Not specified'}
                        </span>
                      </div>
                    )}
                    {ingredient?.is_ingredient_cooking_method === 1 && (
                      <div className="ingredients-card__method">
                        <span className="ingredients-card__method-label">
                          Cook:
                        </span>
                        <span className="ingredients-card__method-value">
                          {ingredient?.ingredient_cooking_method_title ||
                            'Not specified'}
                        </span>
                      </div>
                    )}
                    {ingredient?.ingredient_description && (
                      <div className="ingredients-card__method">
                        <span className="ingredients-card__method-label">
                          Notes:
                        </span>
                        <span className="ingredients-card__method-value">
                          {ingredient?.ingredient_description}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default IngredientsCard;
