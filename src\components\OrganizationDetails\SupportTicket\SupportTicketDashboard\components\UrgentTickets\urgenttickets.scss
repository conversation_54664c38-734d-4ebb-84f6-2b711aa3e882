// Urgent Tickets Component Styles - Using global variables only

.urgent-tickets {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  }

  &__header-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__icon {
    color: var(--color-danger);
  }

  &__title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semi-bold);
    color: var(--text-color-primary);
    margin: 0;
  }

  &__content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    background-color: var(--color-white);
    cursor: pointer;
  }

  &__item-header {
    margin-bottom: var(--spacing-sm);
  }

  &__item-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    line-height: var(--line-height-sm);
    margin: 0;
  }

  &__item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-sm);
  }

  &__item-date {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-secondary);
    margin: 0;
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;
    gap: var(--spacing-md);
  }

  &__empty-icon {
    color: var(--color-success);
    opacity: 0.6;
  }

  &__empty-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semi-bold);
    color: var(--text-color-primary);
    margin: 0;
  }

  &__empty-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    margin: 0;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .urgent-tickets {
    &__header,
    &__content {
      padding: var(--spacing-md);
    }

    &__item {
      padding: var(--spacing-sm);
    }

    &__item-footer {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }

    &__empty {
      height: 250px;
      gap: var(--spacing-sm);
    }

    &__empty-icon {
      width: 36px;
      height: 36px;
    }
  }
}
