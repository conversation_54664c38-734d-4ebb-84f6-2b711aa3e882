'use client';
import React, { useState } from 'react';
import { Formik, Form } from 'formik';
import * as Yup from 'yup';
import CustomDatePicker from '@/components/UI/CustomDatePicker';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomUserSelect from '@/components/UI/CustomUserSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import HeaderImage from '@/components/UI/ImageSecurity';
import { Box, Tooltip, Typography } from '@mui/material';
import dayjs from 'dayjs';
import Profile from '../../../../../../public/images/Companylogo.png';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
import './ticketinformation.scss';

const validationSchema = Yup.object().shape({
  description: Yup.string().required('Description is required'),
  sdate: Yup.date().required('Start Date is required'),
  status: Yup.string().required('Status is required'),
  priority: Yup.string().required('Priority is required'),
  classification: Yup.string().required('Classification is required'),
});

const taskFollowersData = [
  {
    name: 'John Doe',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    name: 'Jane Smith',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    name: 'Alice Johnson',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    name: 'steve smith',
    image:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
];

// User options for CustomUserSelect component
const assigneeOptions = [
  {
    value: 'john_doe',
    label: 'John Doe',
    email: '<EMAIL>',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    value: 'jane_smith',
    label: 'Jane Smith',
    email: '<EMAIL>',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    value: 'alice_johnson',
    label: 'Alice Johnson',
    email: '<EMAIL>',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    value: 'steve_smith',
    label: 'Steve Smith',
    email: '<EMAIL>',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    value: 'michael_chen',
    label: 'Michael Chen',
    email: '<EMAIL>',
    avatar:
      'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
  },
];

export default function TicketInformation({ ticket }) {
  const [taskFollowers, setTaskFollowers] = useState(taskFollowersData || []);
  const [isAddFollowerOpen, setIsAddFollowerOpen] = useState(false);
  // Filter available followers based on search input
  const statusOptions = [
    { label: 'Open', value: 'open' },
    { label: 'Escalated', value: 'escalated' },
    { label: 'In-Progress', value: 'in_progress' },
    { label: 'Invoiced', value: 'invoiced' },
    { label: 'On Hold', value: 'on_hold' },
    { label: 'QA Review', value: 'qa_review' },
    { label: 'Assigned', value: 'assigned' },
    { label: 'Under Review', value: 'under_review' },
    { label: 'Closed', value: 'closed' },
  ];

  const priorityOptions = [
    { value: 'none', label: 'None' },
    { value: 'low', label: 'Low' },
    { value: 'medium', label: 'Medium' },
    { value: 'high', label: 'High' },
    { value: 'urgent', label: 'Urgent' },
  ];

  const classificationOptions = [
    { value: 'question', label: 'Question' },
    { value: 'problem', label: 'Problem' },
    { value: 'feature', label: 'Features' },
    { value: 'others', label: 'Others' },
  ];

  // Add follower to the task
  const addFollower = (follower) => {
    setTaskFollowers([...taskFollowers, follower]);
  };

  // Remove a follower from the task
  const removeFollowers = (index) => {
    const existingFollowers = taskFollowers?.filter(
      (follower, i) => i !== index
    );
    setTaskFollowers(existingFollowers);
  };

  // Get available followers (those who are not yet added)
  const availableFollowers = taskFollowersData
    ?.filter(
      (follower) =>
        !taskFollowers?.some(
          (taskFollower) => taskFollower?.name === follower?.name
        )
    )
    .map((follower) => ({
      value: follower.name.toLowerCase().replace(/\s+/g, '_'),
      label: follower.name,
      email: `${follower.name.toLowerCase().replace(/\s+/g, '.')}@company.com`,
      avatar:
        follower.image ||
        'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D', // Use Unsplash image as fallback
    }));

  const handleToggleClick = () => {
    setIsAddFollowerOpen((prevState) => !prevState);
  };
  return (
    <Formik
      initialValues={{
        description: '',
        sdate: '',
        status: '',
        priority: 'none',
        classification: '',
        assignee: null,
      }}
      validationSchema={validationSchema}
      onSubmit={() => {
        // handle submit here
      }}
    >
      {({
        values,
        errors,
        touched,
        setFieldValue,
        handleBlur,
        handleChange,
        handleSubmit,
      }) => (
        <Form onSubmit={handleSubmit} className="form-wrap">
          {/* Key Information Section */}
          <Box className="form-section">
            <Typography className="body-sm pt8 pb16">
              Key Information
            </Typography>

            {/* Assignee Section */}
            <Box className="field-group">
              <Box className="field-row d-flex align-center gap-sm">
                <Typography className="body-sm">Assignee</Typography>
                <CustomUserSelect
                  className="assignee-select"
                  name="assignee"
                  placeholder="Select assignee..."
                  options={assigneeOptions}
                  value={values.assignee}
                  onChange={(selectedOption) =>
                    setFieldValue('assignee', selectedOption)
                  }
                  isSearchable={true}
                  isClearable={true}
                  required={false}
                />
              </Box>
            </Box>

            {/* Ticket Owner Section */}
            <Box className="field-group pt8">
              <Typography className="body-sm pb16">Ticket Owner</Typography>
              <Box className="info-wrap d-flex align-center gap-sm">
                <HeaderImage
                  imageUrl={Profile}
                  type="avtar"
                  className="preview-img"
                  sx={{ width: 40, height: 40 }}
                  IsExternal={false}
                />
                <Typography className="profile-name body-sm">
                  {ticket?.userName || 'John Doe'}
                </Typography>
              </Box>
            </Box>

            {/* Status and Date Section */}
            <Box className="custom-select-wrap">
              <Box>
                <CustomSelect
                  label="Status"
                  name="status"
                  placeholder="Select Status"
                  options={statusOptions}
                  value={
                    statusOptions?.find(
                      (opt) => opt?.value === values?.status
                    ) || ''
                  }
                  onChange={(selectedOption) =>
                    setFieldValue('status', selectedOption?.value || '')
                  }
                  error={Boolean(touched?.status && errors?.status)}
                  helperText={touched?.status && errors?.status}
                  required
                />
              </Box>
              <Box>
                <CustomDatePicker
                  label=" Start Date ( DD/MM/YYYY )"
                  name="sdate"
                  value={dayjs(values?.sdate)}
                  onBlur={handleBlur}
                  onChange={(date) => setFieldValue('sdate', date)}
                  disablePast
                  inputVariant="outlined"
                  format="DD/M/YYYY"
                  error={Boolean(touched?.sdate && errors?.sdate)}
                  helperText={touched?.sdate && errors?.sdate}
                  required
                />
              </Box>
            </Box>
          </Box>

          {/* Task Followers Section */}
          <Box className="form-section">
            <Box className="followers-wrap d-flex pt8">
              <Typography className="body-sm">Task Followers</Typography>
              <Box className="followers-list-wrap gap-16">
                <Box className="followers-list d-flex">
                  {taskFollowers?.map((follower, index) => {
                    return (
                      <Tooltip
                        title={
                          <Typography className="sub-title-text">
                            {follower?.name}
                          </Typography>
                        }
                        classes={{
                          tooltip: 'info-tooltip-container ',
                        }}
                        arrow
                        key={index}
                      >
                        <Box className="follower-item d-flex align-center gap-8">
                          <HeaderImage
                            imageUrl={follower?.image}
                            type="avtar"
                            className="follower-img"
                            sx={{ width: 30, height: 30 }}
                            IsExternal={true}
                            alt={follower?.name}
                          />
                          <Box className="close-icon-wrap">
                            <CloseIcon
                              onClick={() => removeFollowers(index)}
                              className="close-icon"
                              sx={{ cursor: 'pointer' }}
                            />
                          </Box>
                        </Box>
                      </Tooltip>
                    );
                  })}
                </Box>

                <Box className="add-follower-section d-flex flex-col gap-sm align-start pt8">
                  <Box
                    className="add-follower-wrap d-flex align-center"
                    onClick={handleToggleClick}
                  >
                    <AddIcon className="add-btn" />
                    <Typography className="add-follow-text body-sm">
                      Add Followers
                    </Typography>
                  </Box>

                  {isAddFollowerOpen && (
                    <Box className="follower-select-container">
                      <CustomUserSelect
                        placeholder="Search and select follower..."
                        className="followers-search"
                        options={availableFollowers}
                        value={null}
                        onChange={(selectedOption) => {
                          if (selectedOption) {
                            // Convert CustomUserSelect format back to taskFollower format
                            const newFollower = {
                              name: selectedOption.label,
                              image: selectedOption.avatar
                                ? selectedOption.avatar
                                : 'https://images.unsplash.com/photo-1633332755192-727a05c4013d?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
                            };
                            addFollower(newFollower);
                            setIsAddFollowerOpen(false);
                          }
                        }}
                        isSearchable={true}
                        isClearable={false}
                        menuPosition="absolute"
                      />
                    </Box>
                  )}
                </Box>
              </Box>
            </Box>
          </Box>

          {/* Additional Information Section */}
          <Box className="form-section">
            <Typography className="body-sm pt8 pb16">
              Additional Information
            </Typography>

            <Box className="custom-select-wrap">
              <Box>
                <CustomSelect
                  label="Priority"
                  name="priority"
                  placeholder="Select priority"
                  options={priorityOptions}
                  value={
                    priorityOptions?.find(
                      (opt) => opt?.value === values?.priority
                    ) || ''
                  }
                  onChange={(selectedOption) =>
                    setFieldValue('priority', selectedOption?.value || '')
                  }
                />
              </Box>
              <Box>
                <CustomSelect
                  label="Classifications"
                  name="classification"
                  placeholder="Select Classification"
                  options={classificationOptions}
                  value={
                    classificationOptions?.find(
                      (opt) => opt?.value === values?.classification
                    ) || ''
                  }
                  onChange={(selectedOption) =>
                    setFieldValue('classification', selectedOption?.value || '')
                  }
                  error={Boolean(
                    touched?.classification && errors?.classification
                  )}
                  helperText={touched?.classification && errors?.classification}
                  required
                />
              </Box>
            </Box>
          </Box>

          {/* Description Section */}
          <Box className="">
            <CustomTextField
              fullWidth
              name="description"
              label="Description"
              placeholder="Enter description..."
              value={values?.description}
              onBlur={handleBlur}
              onChange={handleChange}
              error={Boolean(touched?.description && errors?.description)}
              helperText={touched?.description && errors?.description}
              multiline
              minRows={2}
              maxRows={4}
              required
            />
          </Box>

          {/* Action Buttons */}
          <Box className="form-section">
            <Box className="d-flex justify-end pt16 gap-sm">
              <CustomButton type="button" title="Cancel" variant="outlined" />
              <CustomButton type="submit" title="Submit" variant="contained" />
            </Box>
          </Box>
        </Form>
      )}
    </Formik>
  );
}
