'use client';
import React, { useState, useEffect, useMemo } from 'react';
import {
  Box,
  Typography,
  Tooltip,
  Divider,
  useTheme,
  useMediaQuery,
  gridClasses,
} from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { identifiers } from '@/helper/constants/identifier';
import { ArrowUpward, ArrowDownward } from '@mui/icons-material';
import {
  checkOrganizationRole,
  DateFormat,
  IngredientIconSize,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import { staticOptions } from '@/helper/common/staticOptions';
import { fetchFromStorage, saveToStorage } from '@/helper/context';
import EditIcon from '@/components/ActionIcons/EditIcon';
import DeleteIcon from '@/components/ActionIcons/DeleteIcon';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import DialogBox from '@/components/UI/Modalbox';
import DeleteModal from '@/components/UI/DeleteModal';
import ContentLoader from '@/components/UI/ContentLoader';
import NoDataView from '@/components/UI/NoDataView';
import CustomSelect from '@/components/UI/CustomSelect';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import CheckIcon from '@mui/icons-material/Check';
import AddIcon from '@mui/icons-material/Add';
import CustomOrgPagination from '@/components/UI/customPagination';
import FilterListIcon from '@mui/icons-material/FilterList';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import RecipesIcon from '@/components/UI/RecipePlaceholderIcon/RecipesIcon';
import AddEditAllergen from './AddEditAllergen';
import { getAllergenList, deleteAllergen } from '@/services/recipeService';

const organizationOptions = [
  { value: 'org1', label: 'Organization 1' },
  { value: 'org2', label: 'Organization 2' },
  { value: 'org3', label: 'Organization 3' },
];

const createSortableHeader = (field, label, sortOrder, onSort) => (
  <Box className="d-flex align-center gap-5">
    <Box className="wrap-header-text d-flex align-center">
      <Typography className="title-text fw600">{label}</Typography>
      <Box className="amount-text arrow-wrap">
        {sortOrder?.key === field && sortOrder?.value === 'DESC' ? (
          <ArrowDownward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        ) : (
          <ArrowUpward
            className="arrow-icon cursor-pointer"
            fontSize="small"
            onClick={() => onSort(field)}
          />
        )}
      </Box>
    </Box>
  </Box>
);

const createColumns = (
  sortOrder,
  handleSort,
  handleAddEdit,
  handleDelete,
  currentPage,
  rowsPerPage,
  paginatedData
) => [
  {
    field: 'id',
    headerName: 'ID',
    width: 48,
    minWidth: 48,
    sortable: false,
    flex: 0,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      // Find the index of this row in the paginated data
      const rowIndex = paginatedData.findIndex(
        (row) => row?.id === params?.row?.id
      );
      const sequentialNumber = (currentPage - 1) * rowsPerPage + rowIndex + 1;
      return (
        <Typography className="text-ellipsis">{sequentialNumber}</Typography>
      );
    },
  },
  {
    field: 'name',
    headerName: 'Name',
    width: 250,
    minWidth: 250,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('name', 'Name', sortOrder, handleSort),
    renderCell: (params) => (
      <Box className="h100 d-flex">
        <Box className="d-flex align-center gap-5">
          {params?.row?.iconItem?.iconUrl && (
            <Box className="d-flex">
              <RecipesIcon
                iconUrl={params?.row?.iconItem?.iconUrl}
                altText={params?.row?.attribute_title}
                imgWidth={IngredientIconSize}
                imgHeight={IngredientIconSize}
              />
            </Box>
          )}
          {params?.row?.attribute_description ? (
            <Tooltip
              title={
                <Typography className="sub-title-text">
                  {params?.row?.attribute_description}
                </Typography>
              }
              arrow
              classes={{
                tooltip: 'info-tooltip-container',
              }}
            >
              <Typography className="title-text">
                {params?.row?.attribute_title}
              </Typography>
            </Tooltip>
          ) : (
            <Typography className="title-text">
              {params?.row?.attribute_title}
            </Typography>
          )}
          <Tooltip
            title={
              <>
                {params?.row?.is_system_attribute && (
                  <Typography className="sub-title-text">Default</Typography>
                )}
              </>
            }
            arrow
            classes={{
              tooltip: 'info-tooltip-container',
            }}
          >
            {params?.row?.is_system_attribute && (
              <Typography className="default-d-text fw600">D</Typography>
            )}
          </Tooltip>
        </Box>
      </Box>
    ),
  },

  {
    field: 'status',
    headerName: 'Status',
    width: 200,
    minWidth: 200,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderHeader: () =>
      createSortableHeader('status', 'Status', sortOrder, handleSort),
    renderCell: (params) => {
      return (
        <Box className={'title-text h100 d-flex align-center justify-center '}>
          <Typography
            className={`sub-title-text fw600 ${
              params?.row?.attribute_status === 'active'
                ? 'label-active'
                : params?.row?.attribute_status === 'inactive'
                  ? 'failed'
                  : ''
            }`}
          >
            {params?.row?.attribute_status}
          </Typography>
        </Box>
      );
    },
  },
  {
    field: 'actionBy',
    headerName: 'Action By',
    width: 200,
    minWidth: 200,
    flex: 1,
    sortable: false,
    headerAlign: 'start',
    align: 'start',
    renderCell: (params) => {
      return (
        <Box className="gap-5 h100 d-flex align-center">
          <Box className="text-ellipsis">
            <Typography className="title-text">
              {params?.row?.is_system_attribute
                ? 'System Generated'
                : params?.row?.updated_by_name}
            </Typography>
            <Typography
              className={`title-text ${
                params?.row?.is_system_attribute ? 'system-generated-text' : ''
              }`}
            >
              {DateFormat(params?.row?.updated_at, 'datesWithhour')}
            </Typography>
          </Box>
        </Box>
      );
    },
    // renderHeader: () =>
    //   createSortableHeader('actionBy', 'Action By', sortOrder, handleSort),
  },
  {
    field: 'actions',
    headerName: 'Actions',
    width: 120,
    minWidth: 120,
    flex: 1,
    sortable: false,
    headerAlign: 'center',
    align: 'center',
    renderCell: (params) => {
      return (
        <Box className="d-flex actions align-center justify-center h100">
          {!params?.row?.is_system_attribute ? (
            <>
              <Tooltip
                title={<Typography className="sub-title-text">Edit</Typography>}
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <Box
                  className="d-flex"
                  onClick={() => handleAddEdit(params?.row)}
                >
                  <EditIcon className="edit-action-icon" />
                </Box>
              </Tooltip>
              <Tooltip
                title={
                  <Typography className="sub-title-text">Delete</Typography>
                }
                arrow
                classes={{
                  tooltip: 'info-tooltip-container',
                }}
              >
                <Box className="d-flex">
                  <DeleteIcon
                    className="delete-action-icon"
                    onClick={() => handleDelete(params?.row?.id)}
                  />
                </Box>
              </Tooltip>
            </>
          ) : (
            '-'
          )}
        </Box>
      );
    },
  },
];

export default function AllergenList() {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));

  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [sortOrder, setSortOrder] = useState({ key: '', value: 'ASC' });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleteId, setDeleteId] = useState(null);
  const [allergenData, setAllergenData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isTableAdjusting, setIsTableAdjusting] = useState(false);
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [addEditModal, setAddEditModal] = useState(false);
  const [singleData, setSingleData] = useState('');
  const [filterData, setFilterData] = useState({
    status: '',
    type: '',
    organizationId: '',
  });
  const [filterDataApplied, setFilterDataApplied] = useState({
    status: '',
    type: '',
    organizationId: '',
    searchValue: '',
  });

  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: staticOptions?.ORG_STATUS,
        permission: true,
      },
      {
        key: 'type',
        label: 'Type',
        options: staticOptions?.INGREDIENT_TYPE,
        permission: true,
      },
      {
        key: 'organizationId',
        label: 'Organization',
        options: organizationOptions,
        permission: checkOrganizationRole('super_admin'),
      },
    ],
    []
  );

  // API function to get allergens
  const getAllergensListData = async (
    search,
    page,
    filter,
    Rpp,
    Sort,
    showLoader = true
  ) => {
    try {
      if (showLoader) {
        setIsTableAdjusting(true);
      }
      const response = await getAllergenList(
        search || '',
        page || 1,
        filter || { status: '' },
        Rpp || 10,
        Sort || { key: '', value: 'ASC' }
      );

      setAllergenData(response.allergens);
      setTotalCount(response.totalCount);
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setAllergenData([]);
      setTotalCount(0);
    } finally {
      if (showLoader) {
        setTimeout(() => {
          setIsTableAdjusting(false);
        }, 100);
      }
    }
  };

  // Load initial data on component mount
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setIsTableAdjusting(true);
        const response = await getAllergenList('', 1, { status: '' }, 10, {
          key: '',
          value: 'ASC',
        });

        setAllergenData(response.allergens);
        setTotalCount(response.totalCount);
      } catch (error) {
        setApiMessage('error', error?.response?.data?.message);
        setAllergenData([]);
        setTotalCount(0);
      } finally {
        setTimeout(() => {
          setIsTableAdjusting(false);
        }, 100);
      }
    };

    loadInitialData();
  }, []);

  const toggleFilter = (key) => {
    setSelectedFilters((prevFilters) => {
      if (prevFilters?.includes(key)) {
        return prevFilters?.filter((item) => item !== key);
      } else {
        const index = filters.findIndex((filter) => filter.key === key);
        const newFilters = [...prevFilters];
        newFilters.splice(index, 0, key);
        return newFilters;
      }
    });
  };

  const getFirstFourFilters = () => {
    const savedFilters = fetchFromStorage(identifiers?.ALLERGEN_FILTER);
    setSelectedFilters(savedFilters?.slice(0, 4));
    saveToStorage(identifiers?.ALLERGEN_FILTER, savedFilters?.slice(0, 4));
  };

  const saveLayout = () => {
    saveToStorage(identifiers?.ALLERGEN_FILTER, selectedFilters);
    setOpenFilterDrawer(false);
  };

  useEffect(() => {
    const savedFilters = fetchFromStorage(identifiers?.ALLERGEN_FILTER);
    if (!savedFilters) {
      setSelectedFilters(filters.slice(0, 4).map((filter) => filter.key));
    } else {
      setSelectedFilters(savedFilters);
    }
  }, [filters]);

  const handleDelete = (id) => {
    setDeleteDialogOpen(true);
    setDeleteId(id);
  };

  const handleConfirmDelete = async () => {
    try {
      // Call delete API using service function
      const response = await deleteAllergen(deleteId);

      // Show success message from API or fallback
      setApiMessage(
        'success',
        response?.message || 'Allergen deleted successfully'
      );

      // Refresh the list after deletion
      await getAllergensListData(
        filterDataApplied.searchValue || '',
        currentPage,
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to delete allergen'
      );
    }
    handleCloseDeleteDialog();
  };

  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setDeleteId(null);
  };

  const handleSort = async (key) => {
    const newOrder = sortOrder?.value === 'ASC' ? 'DESC' : 'ASC';
    const newSortOrder = { key, value: newOrder };
    setSortOrder(newSortOrder);
    setCurrentPage(1);

    // Call API with new sort order without showing loader
    await getAllergensListData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      rowsPerPage,
      newSortOrder,
      false // Don't show loader for sorting
    );
  };

  const handleSearch = async () => {
    setIsTableAdjusting(true);
    try {
      // Call API with current search and filter values
      await getAllergensListData(
        searchValue,
        1, // Reset to first page
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
      // Apply current search value to filterDataApplied
      setFilterDataApplied((prev) => ({
        ...prev,
        searchValue: searchValue,
      }));
      setCurrentPage(1);
    } finally {
      // isTableAdjusting will be set to false in the API function
      setTimeout(() => {
        setIsTableAdjusting(false);
      }, 100);
    }
  };

  const handleClearSearch = async () => {
    setIsTableAdjusting(true);
    setSearchValue('');
    setFilterData({
      status: '',
      type: '',
      organizationId: '',
    });
    setFilterDataApplied({
      status: '',
      type: '',
      organizationId: '',
      searchValue: '', // Clear applied search value too
    });
    setCurrentPage(1);

    // Call API with cleared filters
    try {
      await getAllergensListData(
        '',
        1,
        {
          status: '',
          type: '',
          organizationId: '',
          searchValue: '',
        },
        rowsPerPage,
        sortOrder
      );
    } finally {
      setTimeout(() => {
        setIsTableAdjusting(false);
      }, 100);
    }
  };

  const handleApplyFilter = async () => {
    setIsTableAdjusting(true);
    try {
      setCurrentPage(1);
      const newFilterData = {
        status: filterData?.status,
        type: filterData?.type,
        organizationId: filterData?.organizationId,
        searchValue: searchValue,
      };
      setFilterDataApplied(newFilterData);

      // Call API with updated filter data
      await getAllergensListData(
        searchValue,
        1,
        newFilterData,
        rowsPerPage,
        sortOrder
      );

      if (isMobile) {
        setOpenFilterDrawer(false);
      }
    } finally {
      setTimeout(() => {
        setIsTableAdjusting(false);
      }, 100);
    }
  };

  const handleClearFilter = async () => {
    await handleClearSearch();
    if (isMobile) {
      setOpenFilterDrawer(false);
    }
  };

  const handleKeyPress = async (event) => {
    if (event?.key === 'Enter') {
      await handleSearch();
      setOpenFilterDrawer(false);
    }
  };

  // Pagination handlers
  const handlePageChange = async (newPage) => {
    setCurrentPage(newPage);
    await getAllergensListData(
      filterDataApplied.searchValue || '',
      newPage,
      filterDataApplied,
      rowsPerPage,
      sortOrder
    );
  };

  const handleRowsPerPageChange = async (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setCurrentPage(1);
    await getAllergensListData(
      filterDataApplied.searchValue || '',
      1,
      filterDataApplied,
      newRowsPerPage,
      sortOrder
    );
  };

  const handleOpenAddEditModal = (item) => {
    setSingleData(item);
    setAddEditModal(true);
  };

  const handleCloseAddEditModal = async (shouldRefresh = false) => {
    setAddEditModal(false);
    setSingleData('');

    if (shouldRefresh) {
      await getAllergensListData(
        filterDataApplied.searchValue || '',
        currentPage,
        filterDataApplied,
        rowsPerPage,
        sortOrder
      );
    }
  };

  const columns = createColumns(
    sortOrder,
    handleSort,
    handleOpenAddEditModal,
    handleDelete,
    currentPage,
    rowsPerPage,
    allergenData
  );

  return (
    <Box className="allergen-list-container h100">
      <Box className="recipe-category-filter-wrap">
        <Box className="section-right-title">
          <Typography className="sub-header-text">Allergen</Typography>
        </Box>
        <Box className="mr8 pr4">
          <CustomButton
            title="Add Allergen"
            startIcon={<AddIcon />}
            onClick={() => handleOpenAddEditModal('')}
          />
        </Box>
      </Box>
      <Divider />
      <Box className="section-right-content ingredient-list-container">
        <Box className="search-section-wrap">
          {!isMobile &&
            selectedFilters?.map((key) => {
              const filter = filters?.find((f) => f?.key === key);
              return filter?.permission ? (
                <React.Fragment key={key}>
                  {key === 'search' ? (
                    <Box className="search-section-fields">
                      <CustomSearch
                        fullWidth
                        setSearchValue={setSearchValue}
                        onKeyPress={handleKeyPress}
                        searchValue={searchValue}
                      />
                    </Box>
                  ) : (
                    <Box className="search-section-fields">
                      <CustomSelect
                        placeholder={filter?.label}
                        options={filter?.options}
                        value={
                          filter?.options?.find((opt) => {
                            return opt?.value === filterData[key];
                          }) || ''
                        }
                        onChange={(e) =>
                          setFilterData({
                            ...filterData,
                            [key]: e?.value,
                          })
                        }
                        menuPortalTarget={document.body}
                        styles={{
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                      />
                    </Box>
                  )}
                </React.Fragment>
              ) : null;
            })}

          {!isMobile && (
            <>
              <Box>
                <CustomButton
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Apply Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <CheckIcon />
                    </Tooltip>
                  }
                  onClick={handleApplyFilter}
                />
              </Box>
              <Box>
                <CustomButton
                  variant="outlined"
                  isIconOnly
                  startIcon={
                    <Tooltip
                      title={
                        <Typography className="sub-title-text">
                          Clear Filter
                        </Typography>
                      }
                      arrow
                      classes={{
                        tooltip: 'info-tooltip-container',
                      }}
                    >
                      <ClearOutlinedIcon />
                    </Tooltip>
                  }
                  onClick={handleClearFilter}
                />
              </Box>
            </>
          )}
          <Box>
            <CustomButton
              isIconOnly
              startIcon={
                <Tooltip
                  title={
                    <Typography className="sub-title-text">Filters</Typography>
                  }
                  classes={{
                    tooltip: 'info-tooltip-container',
                  }}
                  arrow
                >
                  <FilterListIcon />
                </Tooltip>
              }
              onClick={() => {
                setOpenFilterDrawer(true);
              }}
            />
          </Box>
        </Box>
        <Box className="table-container table-layout">
          {isTableAdjusting ? (
            <ContentLoader />
          ) : allergenData.length === 0 ? (
            <NoDataView
              title="No Allergen found"
              description="There is no Allergen available at the moment."
            />
          ) : (
            <>
              <DataGrid
                key={`datagrid-${allergenData.length}-${currentPage}`}
                rows={allergenData}
                columns={columns}
                pageSize={rowsPerPage}
                rowCount={totalCount}
                checkboxSelection={false}
                disableSelectionOnClick
                hideMenuIcon
                paginationMode="server"
                disableVirtualization={false}
                getRowHeight={() => 'auto'}
                sx={{
                  transition: 'none !important',
                  animation: 'none !important',
                  '& *': {
                    transition: 'none !important',
                    animation: 'none !important',
                    transform: 'none !important',
                  },
                  [`& .${gridClasses.cell}`]: {
                    transition: 'none',
                  },
                }}
              />
              <CustomOrgPagination
                currentPage={currentPage}
                totalCount={totalCount}
                rowsPerPage={rowsPerPage}
                onPageChange={handlePageChange}
                OnRowPerPage={handleRowsPerPageChange}
              />
            </>
          )}
        </Box>
        <RightDrawer
          anchor={'right'}
          open={openFilterDrawer}
          onClose={() => setOpenFilterDrawer(false)}
          title="Filter"
          className="filter-options-drawer"
          content={
            <FilterComponent
              filters={filters}
              filterData={filterData}
              setFilterData={setFilterData}
              selectedFilters={selectedFilters}
              toggleFilter={toggleFilter}
              saveLayout={saveLayout}
              setOpenFilterDrawer={setOpenFilterDrawer}
              setSelectedFilters={setSelectedFilters}
              getFirstFourFilters={getFirstFourFilters}
              setSearchValue={setSearchValue}
              searchValue={searchValue}
              handleKeyPress={handleKeyPress}
              isMobile={isMobile}
              handleApplyFilter={handleApplyFilter}
              handleClearFilter={handleClearFilter}
            />
          }
        />
        <DialogBox
          open={deleteDialogOpen}
          handleClose={handleCloseDeleteDialog}
          title="Confirmation"
          className="delete-modal"
          dividerClass="delete-modal-divider"
          content={
            <DeleteModal
              handleCancel={handleCloseDeleteDialog}
              handleConfirm={handleConfirmDelete}
              text="Are you sure you want to delete this? This action cannot be undone."
            />
          }
        />
        <DialogBox
          open={addEditModal}
          handleClose={() => handleCloseAddEditModal(false)}
          title={singleData ? 'Update Allergen' : 'Add Allergen'}
          className="small-dialog-box-container"
          content={
            <AddEditAllergen
              singleData={singleData}
              handleCloseAddEditModal={handleCloseAddEditModal}
            />
          }
        />
      </Box>
    </Box>
  );
}
