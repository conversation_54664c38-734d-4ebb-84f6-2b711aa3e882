'use client';
import {
  Box,
  Divider,
  useTheme,
  useMediaQuery,
  Tooltip,
  Typography,
} from '@mui/material';
import React, { useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';
import Ticket from './Ticket';
import TicketDetails from './TicketDetails';
import NoTickets from './NoTickets';
import CreateTicket from './CreateTicket';

import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import CheckIcon from '@mui/icons-material/Check';
import ClearOutlinedIcon from '@mui/icons-material/ClearOutlined';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import CustomSelect from '@/components/UI/CustomSelect';
import RightDrawer from '@/components/UI/RightDrawer';
import FilterComponent from '@/components/UI/FilterComponent';
import NoDataView from '@/components/UI/NoDataView';
import CustomSearch from '@/components/UI/CustomSearch';
import CustomButton from '@/components/UI/CustomButton';
import './supportticket.scss';
const ticketsDummyData = [
  {
    id: 100,
    subject: 'Login Issue',
    description:
      'Unable to access the system with correct credentials. Getting authentication error repeatedly.',
    name: 'Amit Sharma from InfoSoft',
    userName: 'Amit Sharma',
    organizationName: 'InfoSoft',
    createdAt: new Date('2024-01-15T12:30:00'),
    status: 'open',
    priority: 'high',
    category: 'HRMS',
  },
  {
    id: 101,
    subject: 'Payment Problem',
    description:
      'Payment gateway is not processing transactions properly. Multiple failed attempts reported.',
    name: 'John Doe from CodeLabs',
    userName: 'John Doe',
    organizationName: 'CodeLabs',
    createdAt: new Date('2024-01-15T11:15:00'),
    status: 'in_progress',
    priority: 'medium',
    category: 'PMS',
  },
];
export default function SupportTicket({ selectedTicketId }) {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down(1500));
  const [selectedStatus, setSelectedStatus] = useState('open');
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [isCreateMode, setIsCreateMode] = useState(false);

  // TODO: Temporarily hiding filters - to be re-enabled later
  const [showFilters] = useState(false);

  // Extended tickets data to match AllTicketsList data
  const extendedTicketsData = [
    {
      id: 100,
      subject: 'Cannot login to account',
      description: 'User unable to access their account after password reset',
      name: 'Emily Davis',
      userName: 'Emily Davis',
      organizationName: 'TechCorp',
      createdAt: new Date('2024-01-15T12:30:00'),
      status: 'in_progress',
      priority: 'HIGH',
      category: 'Account Issues',
      assignedTo: 'Sarah Johnson',
    },
    {
      id: 101,
      subject: 'Feature request: Dark mode',
      description: 'Would love to have a dark mode option for better usability',
      name: 'Robert Brown',
      userName: 'Robert Brown',
      organizationName: 'DesignStudio',
      createdAt: new Date('2024-01-14T14:01:00'),
      status: 'open',
      priority: 'LOW',
      category: 'Feature Request',
      assignedTo: 'Mike Wilson',
    },
    {
      id: 102,
      subject: 'Payment processing error',
      description: 'Getting error 500 when trying to process payment',
      name: 'Emily Davis',
      userName: 'Emily Davis',
      organizationName: 'TechCorp',
      createdAt: new Date('2024-01-13T13:01:00'),
      status: 'resolved',
      priority: 'URGENT',
      category: 'Billing',
      assignedTo: 'Mike Wilson',
    },
    {
      id: 103,
      subject: 'Slow page loading',
      description: 'Dashboard takes a very long time to load',
      name: 'Emily Davis',
      userName: 'Emily Davis',
      organizationName: 'TechCorp',
      createdAt: new Date('2024-01-12T12:01:00'),
      status: 'open',
      priority: 'MEDIUM',
      category: 'Performance',
      assignedTo: 'Sarah Johnson',
    },
    // Include original dummy data as well
    ...ticketsDummyData,
  ];

  // Demo tickets data - in real app this would come from API
  const [tickets] = useState(extendedTicketsData);

  // Effect to set selected ticket when selectedTicketId is provided
  React.useEffect(() => {
    if (selectedTicketId) {
      const ticketToSelect = tickets.find(
        (t) => t.id === parseInt(selectedTicketId)
      );
      if (ticketToSelect) {
        setSelectedTicket(ticketToSelect);
        setIsCreateMode(false);
      }
    }
  }, [selectedTicketId, tickets]);

  const hasTickets = tickets.length > 0;

  const [searchValue, setSearchValue] = useState('');

  // Filter related state
  const [openFilterDrawer, setOpenFilterDrawer] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState([
    'search',
    'status',
    'priority',
  ]);
  const [filterData, setFilterData] = useState({
    status: '',
    priority: '',
    assignee: '',
  });

  // Filter configuration
  const filters = useMemo(
    () => [
      {
        key: 'search',
        label: 'Search',
        options: [],
        permission: true,
      },
      {
        key: 'status',
        label: 'Status',
        options: [
          { label: 'Open', value: 'open' },
          { label: 'Escalated', value: 'escalated' },
          { label: 'In-Progress', value: 'in_progress' },
          { label: 'Invoiced', value: 'invoiced' },
          { label: 'On Hold', value: 'on_hold' },
          { label: 'QA Review', value: 'qa_review' },
          { label: 'Assigned', value: 'assigned' },
          { label: 'Under Review', value: 'under_review' },
          { label: 'Closed', value: 'closed' },
        ],
        permission: true,
      },
      {
        key: 'priority',
        label: 'Priority',
        options: [
          { label: 'Low', value: 'low' },
          { label: 'Medium', value: 'medium' },
          { label: 'High', value: 'high' },
          { label: 'Critical', value: 'critical' },
        ],
        permission: true,
      },
      {
        key: 'assignee',
        label: 'Assignee',
        options: [
          { label: 'Unassigned', value: 'unassigned' },
          { label: 'John Doe', value: 'john_doe' },
          { label: 'Jane Smith', value: 'jane_smith' },
        ],
        permission: true,
      },
    ],
    []
  );

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setIsCreateMode(false);
  };

  // Filter functions
  const toggleFilter = (filterKey) => {
    setSelectedFilters((prev) =>
      prev.includes(filterKey)
        ? prev.filter((key) => key !== filterKey)
        : [...prev, filterKey]
    );
  };

  const saveLayout = () => {
    // Implementation for saving filter layout
  };

  const getFirstFourFilters = () => {
    return selectedFilters.slice(0, 4);
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      saveLayout();
    }
  };

  const handleCreateTicket = () => {
    setIsCreateMode(true);
    setSelectedTicket(null);
  };

  const handleCancelCreate = () => {
    setIsCreateMode(false);
    setSelectedTicket(null);
  };

  const handleApplyFilter = () => {
    setOpenFilterDrawer(false);
    // Apply filter logic here - filter tickets based on filterData
    // In real implementation, this would trigger API call with filters
  };

  const handleClearFilter = () => {
    setFilterData({
      status: '',
      priority: '',
      assignee: '',
    });
    setSearchValue('');
  };

  // Back navigation handlers
  const handleBackNavigation = () => {
    if (isCreateMode) {
      // From create mode, go back to dashboard
      router.push('/support-ticket/dashboard');
    } else {
      // From main tickets page, go back to all tickets
      router.push('/support-ticket/all-tickets');
    }
  };

  // Render no tickets view if no tickets exist
  const renderNoTicketsView = () => (
    <Box className="section-wrapper support-ticket-section-wrapper">
      {/* Only Right Section - Create Ticket or Empty State */}
      <Box className="section-right">
        <Box className="show-tickets-wrap">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title">
              <p className="sub-header-text d-flex align-center gap-sm">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={handleBackNavigation}
                />
                {isCreateMode ? 'Create New Ticket' : 'Support Ticket'}
              </p>
            </Box>
            {isCreateMode ? (
              <Box className="mr8 pr4 d-flex align-center gap-sm">
                <CustomButton
                  title="Create Ticket"
                  startIcon={<AddIcon />}
                  onClick={handleCreateTicket}
                />
                <CustomButton
                  title="Cancel"
                  variant="outlined"
                  onClick={handleCancelCreate}
                />
              </Box>
            ) : (
              tickets &&
              tickets.length > 0 && (
                <Box className="mr8 pr4">
                  <CustomButton
                    title="Create New Ticket"
                    startIcon={<AddIcon />}
                    onClick={handleCreateTicket}
                  />
                </Box>
              )
            )}
          </Box>
          <Divider />
          {/* Show CreateTicket form if isCreateMode is true, else show NoTickets */}
          {isCreateMode ? (
            <Box className="section-right-content">
              <CreateTicket onBack={handleCancelCreate} />
            </Box>
          ) : (
            <Box className="section-right-content">
              <NoTickets onAddTicketClick={handleCreateTicket} />
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );

  // Render main tickets view with everything in right section
  const renderTicketsView = () => (
    <Box className="section-wrapper support-ticket-section-wrapper">
      {/* Right Section - Everything */}
      <Box className="section-right">
        <Box className="show-tickets-wrap">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title">
              <p className="sub-header-text d-flex align-center">
                <ArrowBackIosIcon
                  className="cursor-pointer"
                  onClick={handleBackNavigation}
                />
                {isCreateMode ? 'Create New Ticket' : 'Support Tickets'}
              </p>
            </Box>

            {isCreateMode ? (
              <Box className="mr8 pr4 d-flex align-center gap-sm">
                <CustomButton
                  title="Create Ticket"
                  startIcon={<AddIcon />}
                  onClick={handleCreateTicket}
                />
                <CustomButton
                  title="Cancel"
                  variant="outlined"
                  onClick={handleCancelCreate}
                />
              </Box>
            ) : (
              tickets &&
              tickets.length > 0 && (
                <Box className="mr8 pr4 d-flex align-center gap-sm">
                  {/* TODO: Filters temporarily hidden - will be re-enabled later */}
                  {showFilters && (
                    <Box className="search-section-wrap">
                      {!isMobile &&
                        selectedFilters?.map((key) => {
                          const filter = filters?.find((f) => f?.key === key);
                          return filter?.permission ? (
                            <React.Fragment key={key}>
                              {key === 'search' ? (
                                <Box className="search-section-fields">
                                  <CustomSearch
                                    fullWidth
                                    setSearchValue={setSearchValue}
                                    onKeyPress={handleKeyPress}
                                    searchValue={searchValue}
                                  />
                                </Box>
                              ) : (
                                <Box className="search-section-fields">
                                  <CustomSelect
                                    placeholder={filter?.label}
                                    options={filter?.options}
                                    value={
                                      filter?.options?.find((opt) => {
                                        return opt?.value === filterData[key];
                                      }) || ''
                                    }
                                    onChange={(e) =>
                                      setFilterData({
                                        ...filterData,
                                        [key]: e?.value,
                                      })
                                    }
                                    menuPortalTarget={document.body}
                                    styles={{
                                      menuPortal: (base) => ({
                                        ...base,
                                        zIndex: 9999,
                                      }),
                                    }}
                                  />
                                </Box>
                              )}
                            </React.Fragment>
                          ) : null;
                        })}

                      {!isMobile && (
                        <>
                          <Box>
                            <CustomButton
                              isIconOnly
                              startIcon={
                                <Tooltip
                                  title={
                                    <Typography className="sub-title-text">
                                      Apply Filter
                                    </Typography>
                                  }
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                >
                                  <CheckIcon />
                                </Tooltip>
                              }
                              onClick={handleApplyFilter}
                            />
                          </Box>
                          <Box>
                            <CustomButton
                              variant="outlined"
                              isIconOnly
                              startIcon={
                                <Tooltip
                                  title={
                                    <Typography className="sub-title-text">
                                      Clear Filter
                                    </Typography>
                                  }
                                  arrow
                                  classes={{
                                    tooltip: 'info-tooltip-container',
                                  }}
                                >
                                  <ClearOutlinedIcon />
                                </Tooltip>
                              }
                              onClick={handleClearFilter}
                            />
                          </Box>
                        </>
                      )}
                    </Box>
                  )}
                  {showFilters && (
                    <CustomButton
                      isIconOnly
                      startIcon={
                        <Tooltip
                          title={<p className="sub-title-text">Filters</p>}
                          classes={{ tooltip: 'info-tooltip-container' }}
                          arrow
                        >
                          <FilterListIcon />
                        </Tooltip>
                      }
                      onClick={() => {
                        setOpenFilterDrawer(true);
                      }}
                    />
                  )}
                  <CustomButton
                    title="Create New Ticket"
                    startIcon={<AddIcon />}
                    onClick={handleCreateTicket}
                  />
                </Box>
              )
            )}
          </Box>
          <Divider />

          {/* Right Section Content */}
          {isCreateMode ? (
            <Box className="section-right-content">
              <CreateTicket onBack={handleCancelCreate} />
            </Box>
          ) : (
            <Box className="section-right-content support-ticket-container">
              {/* Tickets List Section */}
              <Box className="tickets-list-section">
                {/* Tickets List and Details Side by Side */}
                <Box className="d-flex align-start gap-4 tickets-details-container">
                  {/* Tickets List */}
                  <Box className="tickets-list-wrapper">
                    <Ticket
                      selectedStatus={selectedStatus}
                      setSelectedStatus={setSelectedStatus}
                      onTicketClick={handleTicketClick}
                      ticketsList={tickets}
                      selectedTicket={selectedTicket}
                      hideStatusChip={true}
                      showBottomPadding={true}
                    />
                  </Box>

                  {/* Ticket Details */}
                  <Box className="ticket-details-content">
                    {selectedTicket ? (
                      <TicketDetails ticket={selectedTicket} />
                    ) : (
                      <Box className="no-data-container">
                        <NoDataView
                          title="No Ticket Selected"
                          description="Please select a ticket from the list to view its details, or create a new ticket."
                        />
                      </Box>
                    )}
                  </Box>
                </Box>
              </Box>

              <Box className="drawer-wrap">
                <RightDrawer
                  className="filter-options-drawer"
                  anchor="right"
                  open={openFilterDrawer}
                  onClose={() => setOpenFilterDrawer(false)}
                  title="Filter"
                  content={
                    <Box>
                      <FilterComponent
                        filters={filters}
                        filterData={filterData}
                        setFilterData={setFilterData}
                        selectedFilters={selectedFilters}
                        toggleFilter={toggleFilter}
                        saveLayout={saveLayout}
                        setOpenFilterDrawer={setOpenFilterDrawer}
                        setSelectedFilters={setSelectedFilters}
                        getFirstFourFilters={getFirstFourFilters}
                        setSearchValue={setSearchValue}
                        searchValue={searchValue}
                        handleKeyPress={handleKeyPress}
                        isMobile={isMobile}
                        handleApplyFilter={handleApplyFilter}
                        handleClearFilter={handleClearFilter}
                      />
                    </Box>
                  }
                />
              </Box>
            </Box>
          )}
        </Box>
      </Box>
    </Box>
  );

  return (
    <>
      {/* Show tickets view if tickets exist, otherwise show no tickets view */}
      {hasTickets ? renderTicketsView() : renderNoTicketsView()}
    </>
  );
}
