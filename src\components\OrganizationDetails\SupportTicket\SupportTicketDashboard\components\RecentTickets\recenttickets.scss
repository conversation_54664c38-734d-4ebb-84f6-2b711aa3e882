// Recent Tickets Component Styles - Using global variables only

.recent-tickets {
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    padding: var(--spacing-lg);
    border-bottom: var(--normal-sec-border);
    background-color: var(--color-white);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
  }

  &__title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semi-bold);
    color: var(--text-color-primary);
    margin: 0;
  }

  &__content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
  }

  &__list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
  }

  &__item {
    padding: var(--spacing-md);
    background-color: var(--color-white);
    cursor: pointer;
  }

  &__item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-sm);
    gap: var(--spacing-sm);
  }

  &__item-title {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-color-primary);
    line-height: var(--line-height-sm);
    margin: 0;
    flex: 1;
  }

  &__item-date {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-xs);
    color: var(--text-color-secondary);
    white-space: nowrap;
    margin: 0;
  }

  &__item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__item-badges {
    display: flex;
    gap: var(--spacing-sm);
  }

  &__empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
  }

  &__empty-text {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-sm);
    color: var(--text-color-secondary);
    text-align: center;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .recent-tickets {
    &__header,
    &__content {
      padding: var(--spacing-md);
    }

    &__item {
      padding: var(--spacing-sm);
    }

    &__item-header {
      flex-direction: column;
      align-items: flex-start;
      gap: var(--spacing-xs);
    }

    &__item-badges {
      flex-wrap: wrap;
    }
  }
}
