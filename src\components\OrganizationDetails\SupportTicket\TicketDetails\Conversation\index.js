import { EmptyConversationIcon } from '@/helper/common/images';
import { Box, Typography } from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import React, { useState } from 'react';
import { DateFormat } from '@/helper/common/commonFunctions';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import CustomButton from '@/components/UI/CustomButton';
import './conversation.scss';

export default function Conversation() {
  const [newMessage, setNewMessage] = useState('');
  const [isInternalNote, setIsInternalNote] = useState(false);

  // Sample conversation data - replace with real data from props/API
  const conversationData = [
    {
      id: 1,
      sender: '<PERSON>',
      message:
        "I'm having trouble logging into my account. It keeps saying my password is incorrect.",
      timestamp: new Date('2024-01-15T15:30:00'),
      isUser: true,
    },
    {
      id: 2,
      sender: '<PERSON>',
      message:
        "Hi <PERSON>, I'll help you with this. Let me check your account status.",
      timestamp: new Date('2024-01-15T16:00:00'),
      isUser: false,
    },
    {
      id: 3,
      sender: 'Sarah Johnson',
      message: 'Internal note: Account locked due to multiple failed attempts.',
      timestamp: new Date('2024-01-15T16:02:00'),
      isUser: false,
      isInternalNote: true,
    },
  ];

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      setNewMessage('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (conversationData.length === 0) {
    return (
      <Box className="convesation-wrap d-flex flex-col align-center justify-center text-align pb32">
        <EmptyConversationIcon className="conversation-icon" />
        <Box>
          <Typography className="conversation-text body-sm">
            No Conversation available
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box className="conversation-container">
      <Box className="conversation-messages">
        {conversationData.map((message) => (
          <Box
            key={message.id}
            className={`message-item ${message.isInternalNote ? 'internal-note' : ''}`}
          >
            <Box className="message-header">
              <Typography className="sender-name body-sm">
                {message.sender}
                {message.isInternalNote && (
                  <span className="internal-note-badge">Internal Note</span>
                )}
              </Typography>
              <Typography className="message-timestamp body-xs">
                {DateFormat(message.timestamp, 'datesWithhour')}
              </Typography>
            </Box>
            <Box className="message-content">
              <Typography className="message-text body-sm">
                {message.message}
              </Typography>
            </Box>
          </Box>
        ))}
      </Box>

      {/* Internal note checkbox */}
      <Box className="internal-note-checkbox d-flex align-center">
        <CustomCheckbox
          checked={isInternalNote}
          onChange={(e) => setIsInternalNote(e.target.checked)}
          name="internal_note"
        />
        <Box className="d-flex align-center gap-sm">
          {isInternalNote ? (
            <VisibilityIcon className="visibility-icon active" />
          ) : (
            <VisibilityOffIcon className="visibility-icon" />
          )}
          <Typography className="checkbox-label body-sm">
            Internal note (visible to agents only)
          </Typography>
        </Box>
      </Box>

      {/* Message input */}
      <Box className="message-input-container">
        <Box className="conversation-input-wrapper d-flex align-center gap-sm">
          <Box className="conversation-input-container">
            <CustomTextField
              className="conversation-input"
              fullWidth
              placeholder="Add a comment..."
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              variant="outlined"
              maxRows={3}
              minRows={3}
              multiline
            />
          </Box>
          <CustomButton
            startIcon={<SendIcon />}
            isIconOnly
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            className="conversation-send-button-icon"
            variant="outlined"
          />
        </Box>
      </Box>
    </Box>
  );
}
