import { Box, Typography } from '@mui/material';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import NoDataView from '@/components/UI/NoDataView';

const DietarySuitability = ({
  values,
  setFieldValue,
  isDefault,
  dietarySuitability,
}) => {
  return (
    <>
      <Typography className="sub-header-text pt16 pb8">
        Dietary Suitability
      </Typography>
      {!dietarySuitability || dietarySuitability.length === 0 ? (
        <NoDataView
          title="No Dietary Suitability Fields Available"
          description="There are no dietary suitability fields configured at the moment."
        />
      ) : (
        <Box className="ingredient-allergens-grid-container">
          {dietarySuitability?.map((suitability) => (
            <CustomCheckbox
              key={suitability?.id}
              name={`dietary_${suitability?.id}`}
              checked={values[`dietary_${suitability?.id}`]}
              label={suitability?.label}
              onChange={(e) =>
                setFieldValue(`dietary_${suitability?.id}`, e.target.checked)
              }
              disabled={isDefault}
            />
          ))}
        </Box>
      )}
    </>
  );
};

export default DietarySuitability;
