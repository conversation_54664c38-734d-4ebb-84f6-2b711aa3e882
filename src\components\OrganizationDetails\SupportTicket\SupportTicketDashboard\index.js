'use client';

import React, { useState, useEffect } from 'react';
import { Box, Typography, Divider } from '@mui/material';
import SimpleStatsCard from './components/SimpleStatsCard';
import RecentTickets from './components/RecentTickets';
import UrgentTickets from './components/UrgentTickets';
import ContentLoader from '@/components/UI/ContentLoader';
import { supportTicketService } from '@/services/supportTicketService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import './supportticketdashboard.scss';

const SupportTicketDashboard = () => {
  const [dashboardData, setDashboardData] = useState({
    totalTickets: 0,
    openTickets: 0,
    inProgressTickets: 0,
    resolvedTickets: 0,
  });
  const [recentTicketsData, setRecentTicketsData] = useState([]);
  const [urgentTicketsData, setUrgentTicketsData] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch dashboard data from API
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await supportTicketService.getDashboardData();

        if (data) {
          // Update dashboard stats
          setDashboardData({
            totalTickets: data?.totalTickets || 0,
            openTickets: data?.openTickets || 0,
            inProgressTickets: data?.inProgressTickets || 0,
            resolvedTickets: data?.resolvedTickets || 0,
          });

          // Update recent tickets (use API data or fallback)
          setRecentTicketsData(data?.recentTickets || fallbackRecentTickets);

          // Update urgent tickets (use API data or fallback)
          setUrgentTicketsData(data?.urgentTickets || fallbackUrgentTickets);
        }
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setApiMessage('error', 'Failed to load dashboard data');

        // Keep default/fallback data if API fails
        setDashboardData({
          totalTickets: 5,
          openTickets: 2,
          inProgressTickets: 1,
          resolvedTickets: 1,
        });
        setRecentTicketsData(fallbackRecentTickets);
        setUrgentTicketsData(fallbackUrgentTickets);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // Simple statistics data - clean design
  const statisticsData = [
    {
      id: 'total-tickets',
      title: 'Total Tickets',
      value: dashboardData.totalTickets.toString(),
      icon: 'BarChart3',
      color: 'primary',
    },
    {
      id: 'open-tickets',
      title: 'Open Tickets',
      value: dashboardData.openTickets.toString(),
      icon: 'AlertCircle',
      color: 'warning',
    },
    {
      id: 'in-progress',
      title: 'In Progress',
      value: dashboardData.inProgressTickets.toString(),
      icon: 'Clock',
      color: 'accent',
    },
    {
      id: 'resolved',
      title: 'Resolved',
      value: dashboardData.resolvedTickets.toString(),
      icon: 'CheckCircle',
      color: 'success',
    },
  ];

  // Fallback data for tickets (used when API doesn't provide data)
  const fallbackRecentTickets = [
    {
      id: 1,
      title: 'Cannot login to account',
      status: 'in progress',
      priority: 'high',
      date: '15/01/2024',
    },
    {
      id: 2,
      title: 'Feature request: Dark mode',
      status: 'open',
      priority: 'low',
      date: '14/01/2024',
    },
    {
      id: 3,
      title: 'Payment processing error',
      status: 'resolved',
      priority: 'urgent',
      date: '13/01/2024',
    },
    {
      id: 4,
      title: 'Slow page loading',
      status: 'open',
      priority: 'medium',
      date: '12/01/2024',
    },
    {
      id: 5,
      title: 'Email notifications not working',
      status: 'closed',
      priority: 'medium',
      date: '11/01/2024',
    },
  ];

  const fallbackUrgentTickets = [
    {
      id: 1,
      title: 'Payment processing error',
      status: 'resolved',
      date: '13/01/2024',
    },
  ];

  const handleStatisticClick = () => {
    // Handle navigation or filtering based on statistic
    // Could navigate to filtered tickets view
  };

  return (
    <Box className="section-wrapper">
      <Box className="section-right">
        <Box className="section-right-tab-header">
          <Box className="d-flex align-center justify-space-between">
            <Box className="section-right-title d-flex align-center gap-sm">
              <Typography className="sub-header-text">Dashboard</Typography>
            </Box>
          </Box>
          <Divider />
        </Box>

        <Box className="section-right-content">
          <Box className="support-ticket-dashboard-wrap">
            <Box className="support-ticket-dashboard">
              {/* Header */}
              <Box className="support-ticket-dashboard__header">
                <Typography className="support-ticket-dashboard__subtitle">
                  Overview of your support ticket system
                </Typography>
              </Box>

              {loading ? (
                <ContentLoader />
              ) : (
                <>
                  {/* Statistics Grid */}
                  <Box className="support-ticket-dashboard__stats-grid">
                    {statisticsData.map((stat) => (
                      <SimpleStatsCard
                        key={stat.id}
                        {...stat}
                        onClick={handleStatisticClick}
                      />
                    ))}
                  </Box>

                  {/* Content Grid */}
                  <Box className="support-ticket-dashboard__content-grid">
                    {/* Recent Tickets */}
                    <Box className="support-ticket-dashboard__recent-tickets">
                      <RecentTickets tickets={recentTicketsData} />
                    </Box>

                    {/* Urgent Tickets */}
                    <Box className="support-ticket-dashboard__urgent-tickets">
                      <UrgentTickets tickets={urgentTicketsData} />
                    </Box>
                  </Box>
                </>
              )}
            </Box>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default SupportTicketDashboard;
