'use client';

import React, { useEffect, useContext, useState, useRef } from 'react';
import { Box, Divider } from '@mui/material';
import AuthContext from '@/helper/authcontext';
import PreLoader from '@/components/UI/Loader';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import Onboarding from '../Onboarding';
import ProfileDetails from '../ProfileDetails';
import useRoleList from '@/hooks/useRoleList';
import useLocationData from '@/hooks/useLocationData';
import { useRouter } from 'next/navigation';
import { fetchFromStorage, saveToStorage } from '@/helper/context/storage';
import { identifiers } from '@/helper/constants/identifier';
import DialogBox from '@/components/UI/Modalbox';
import ConfirmationModal from '@/components/UI/ConfirmationModal';
import {
  checkOrganizationRole,
  setApiMessage,
} from '@/helper/common/commonFunctions';
import '../UserProfile/user.scss';

const MyProfile = () => {
  const { authState, setAuthState, setRestrictedModal, orgDetails } =
    useContext(AuthContext);
  const formikRef = useRef(null);
  const router = useRouter();
  // const authdata = fetchFromStorage(identifiers?.AUTH_DATA);
  const [UserDetails, setUserDetails] = useState('');
  const [loader, setLoader] = useState(true);
  const {
    countries,
    counties,
    cities,
    setCities,
    setCounties,
    setSelectedCountry,
    setSelectedCounty,
  } = useLocationData();
  const { roleList, fetchRoleList } = useRoleList();
  const [getCheckList, setGetCheckList] = useState([]);
  const [formRightToWork, setFormRightToWork] = useState();
  const [formNewStarterHMRC, setFormNewStarterHMRC] = useState();
  const [formHealthSafety, setFormHealthSafety] = useState();
  const [formEmpContract, setFormEmpContract] = useState();
  const [profileImage, setProfileImage] = useState();
  const [redirectModal, setRedirectModal] = useState(false);
  const [nextRoute, setNextRoute] = useState(null);
  const isNormalUser = fetchFromStorage(identifiers?.NORMAL_LOGIN);
  // let isDeviceId =
  //   typeof window !== 'undefined' && fetchFromStorage(identifiers?.DEVICEID);

  // const ViewAccessOnly = UserDetails && UserDetails?.profile_status;
  const ViewAccessOnly = false;
  const handleDialogResponse = (confirm) => {
    setRedirectModal(false);
    if (confirm && nextRoute) {
      router.replace(nextRoute);
    }
  };
  const handleCloseRedirect = () => {
    setRedirectModal(false);
    handleDialogResponse(false);
  };

  useEffect(() => {
    if (authState?.UserPermission) {
      const isOrgView = checkOrganizationRole('org_master') || false;
      const LOGIN_ORG = fetchFromStorage(identifiers?.LOGIN_ORG);
      const isRestricted = isNormalUser
        ? false
        : isOrgView
          ? !orgDetails?.attributes?.email ||
            !orgDetails?.attributes?.contact_person
          : !authState?.profile_status || authState?.purchase_plan === false;
      if (isRestricted && !LOGIN_ORG) {
        setRestrictedModal({
          isOrgView: isOrgView,
          purchase_plan: authState?.purchase_plan,
          user_status:
            !orgDetails?.attributes?.email ||
            !orgDetails?.attributes?.contact_person,
          profile_status: authState?.profile_status,
        });
      }
    }
  }, [authState?.UserPermission]);

  useEffect(() => {
    const handleRouteChange = (url) => {
      if (
        fetchFromStorage(identifiers?.USER_DATA)?.user_status === 'ongoing' &&
        fetchFromStorage(identifiers?.USER_ONBOARD)
      ) {
        setRedirectModal(true);
        setNextRoute(url);
        throw 'Navigation aborted by the user.';
      } else {
        //
      }
    };

    // router.prefetch();
    const originalPush = router.push;
    router.push = (url, as, options) => {
      handleRouteChange(url);
      return originalPush(url, as, options);
    };

    return () => {
      router.push = originalPush;
    };
  }, [router]);

  // Handle browser's back/forward button
  useEffect(() => {
    const handleBeforeUnload = (event) => {
      event.preventDefault();
      event.returnValue = '';
    };

    window != undefined &&
      window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window != undefined &&
        window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);
  // Get My profile details
  const getUserDetails = async () => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.MY_PROFILE);
      if (status === 200) {
        let filterUserList = data?.data?.user_roles?.map((user) => ({
          label: user?.role_name,
          value: user?.id,
        }));
        let filterBranchList = data?.data?.assign_branch_ids?.map((branch) => ({
          label: branch?.branch_name,
          value: branch?.id,
          color: branch?.branch_color,
        }));
        setUserDetails({
          ...data?.data,
          user_roles: filterUserList,
          assign_branch_ids: filterBranchList,
        });
        const userdata = {
          ...data?.data,
          user_roles: filterUserList,
          assign_branch_ids: filterBranchList,
        };
        setAuthState({
          ...authState,
          ...userdata,
          UserPermission: authState?.UserPermission,
          user_roles: filterUserList,
          assign_branch_ids: filterBranchList,
        });
        data?.data?.user_avatar &&
          setProfileImage({ url: data?.data?.user_avatar, IsFromAPi: true });
        saveToStorage(identifiers?.USER_DATA, data?.data);
        const isSingleRelevantRole =
          data?.data?.user_roles &&
          data?.data?.user_roles?.length === 1 &&
          (data?.data?.web_user_active_role_id === 2 ||
            data?.data?.web_user_active_role_id === 1);
        if (!isSingleRelevantRole) {
          getOnboardingChecklist(true, data?.data);
        }

        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  // List of onboarding check list
  const getOnboardingChecklist = async (IsUpdate, users) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONBOARDING_CHECKLIST
      );

      if (status === 200) {
        setGetCheckList(data?.checklist);
        getOnbordingFormDetails(1, setFormRightToWork);
        getOnbordingFormDetails(2, setFormNewStarterHMRC);
        getOnbordingFormDetails(3, setFormHealthSafety);
        if (
          users?.user_status !== 'active' &&
          users?.user_status !== 'pending'
        ) {
          getOnbordingFormDetails(4, setFormEmpContract);
        }

        if (IsUpdate !== true) {
          getUserDetails();
        }
        const ISCompletedOnboarding = data?.checklist?.find(
          (l) => l?.status !== 'completed' && l?.id !== 3
        );
        saveToStorage(
          identifiers?.USER_ONBOARD,
          ISCompletedOnboarding ? false : true
        );

        setLoader(false);
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };
  // Get Form details by ID
  const getOnbordingFormDetails = async (checkId, setValue) => {
    setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_ONBOARDING_FORM_DETAILS + `?checklist_id=${checkId}`
      );

      if (status === 200) {
        setLoader(false);
        setValue(data?.formDetail);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      console.error('Error fetching onbording emp contract check list:', error);
      setLoader(false);
    }
  };

  // When onboarding all check list completed the user request to admin for verify their check list
  const RequestToAdmin = async (isSave) => {
    !isSave && setLoader(true);
    try {
      const { status, data } = await axiosInstance.get(URLS?.REQUEST_TO_ADMIN);
      if (status === 200 || status === 201) {
        setLoader(false);
        setApiMessage('success', data?.message);
        !isSave && getUserDetails();
      }
    } catch (error) {
      setLoader(false);
      setApiMessage('error', error?.response?.data?.message);
    }
  };

  useEffect(() => {
    getUserDetails();
    fetchRoleList();
  }, []);
  // useEffect(() => {
  //   if (!isDeviceId) {
  //     window.location.reload();
  //   }
  // }, [isDeviceId]);

  return (
    <Box className="user-container">
      {loader ? (
        <PreLoader />
      ) : (
        <>
          <Box className="User-profile-page">
            <Box className="user-section">
              <ProfileDetails
                UserDetails={UserDetails}
                setUserDetails={setUserDetails}
                profileImage={profileImage}
                setProfileImage={setProfileImage}
                formikRef={formikRef}
                countries={countries}
                counties={counties}
                cities={cities}
                setCities={setCities}
                setCounties={setCounties}
                setSelectedCountry={setSelectedCountry}
                setSelectedCounty={setSelectedCounty}
                roleList={roleList}
                ViewAccessOnly={ViewAccessOnly}
                isMyProfile={true}
                getUserDetails={getUserDetails}
              />
            </Box>
            {((UserDetails?.web_user_active_role_id !== 2 &&
              UserDetails?.web_user_active_role_id !== 1) ||
              (UserDetails?.user_roles &&
                UserDetails?.user_roles.length > 1)) && (
              <>
                <Divider className="mb16 mt16" />
                <Onboarding
                  isMyProfile={true}
                  getCheckList={getCheckList}
                  formRightToWork={formRightToWork}
                  formNewStarterHMRC={formNewStarterHMRC}
                  formHealthSafety={formHealthSafety}
                  UserDetails={UserDetails}
                  RequestToAdmin={RequestToAdmin}
                  formEmpContract={formEmpContract}
                  getOnboardingChecklist={getOnboardingChecklist}
                  ViewAccessOnly={false}
                  loader={loader}
                  countryList={countries}
                />
              </>
            )}
          </Box>
          <DialogBox
            open={redirectModal}
            handleClose={() => {
              handleCloseRedirect();
            }}
            title={'Confirmation'}
            className="confirmation-modal"
            dividerClass="confirmation-modal-divider"
            content={
              <>
                <ConfirmationModal
                  handleCancel={handleCloseRedirect}
                  handleConfirm={() => {
                    handleDialogResponse(true);
                    RequestToAdmin(true);
                  }}
                  confirmText="Save"
                  text="To finish your onboarding, please click 'Save' to submit it for review."
                />
              </>
            }
          />
        </>
      )}
    </Box>
  );
};

export default MyProfile;
