import React from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomTextField from '@/components/UI/CustomTextField';
import './ScalingCard.scss';
import { Box } from '@mui/material';

const ScalingCard = ({
  scalingType,
  customValue,
  scalingOptions,
  onScalingTypeChange,
  onCustomValueChange,
}) => {
  return (
    <div className="scaling-card hide-on-print">
      <div className="scaling-card__header">
        <p className="scaling-card__title">
          <Icon name="Scale" size={20} color="currentColor" />
          <span>Scale Recipe</span>
        </p>
      </div>
      <div className="scaling-card__content">
        <div>
          <CustomSelect
            options={scalingOptions}
            value={scalingOptions?.find(
              (option) => option.value === scalingType
            )}
            onChange={(e) => {
              onScalingTypeChange(e.value);
            }}
            placeholder="Scale"
            isClearable={false}
            label="Scale factor"
            name="scaling-select"
          />
          {scalingType === 'custom' && (
            <Box className="mt8">
              <CustomTextField
                fullWidth
                id="scaling"
                name="scaling"
                label="Custom Scale factor"
                placeholder="Enter Custom Scale factor"
                value={customValue}
                onChange={(e) => {
                  // Only allow numbers
                  const val = e.target.value.replace(/[^0-9]/g, '');
                  onCustomValueChange(val);
                }}
              />
            </Box>
          )}
        </div>
      </div>
    </div>
  );
};

export default ScalingCard;
