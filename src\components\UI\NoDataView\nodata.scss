@import '@/styles/variable.scss';

.no-data-wrap {
  text-align: center;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  width: 100%;
  max-width: 400px;
  margin: auto;
  height: calc(100vh - 350px);
  margin-top: var(--spacing-xxl);

  .no-data-image {
    width: 120px;
    height: 120px;
    margin-bottom: var(--spacing-md);

    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .no-data-title {
    color: var(--text-color-primary);
    margin-bottom: var(--spacing-sm);
  }

  .no-data-description {
    color: var(--text-color-slate-gray);
  }
}
body {
  .no-data-auto-height-conainer {
    height: auto;
    margin-top: var(--spacing-none);
  }
  .no-data-auto-margin-height-conainer {
    height: auto;
    margin-top: auto;
  }
}
