import React, { useState, useEffect } from 'react';
import Icon from '@/components/UI/AppIcon/AppIcon';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomButton from '@/components/UI/CustomButton';
import CustomSelect from '@/components/UI/CustomSelect';
import { InputAdornment } from '@mui/material';
import { useRouter } from 'next/navigation';
import {
  getIngredientItemsList,
  getAttributeList,
  getRecipeMeasuresList,
} from '@/services/recipeService';
import { setApiMessage } from '@/helper/common/commonFunctions';
import { convertCost } from '@/helper/common/unitConversion';
import CustomCheckbox from '@/components/UI/CustomCheckbox';
import './IngredientsSection.scss';

const IngredientsSection = ({
  formData,
  dispatch,
  validationErrors = {},
  currency,
}) => {
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [unitsOfMeasureOptions, setUnitsOfMeasureOptions] = useState([]);
  const [localIngredients, setLocalIngredients] = useState([]);

  // API-driven ingredient database state
  const [ingredientDatabase, setIngredientDatabase] = useState([]);
  const [isLoadingIngredients, setIsLoadingIngredients] = useState(false);
  const [searchTimeout, setSearchTimeout] = useState(null);
  const [cookingMethods, setCookingMethods] = useState([]);
  const [preparationMethods, setPreparationMethods] = useState([]);
  const [isCookingMethod, setIsCookingMethod] = useState(
    formData?.isCookingMethod || false
  );
  const [isPreparationMethod, setIsPreparationMethod] = useState(
    formData?.isPreparationMethod || false
  );
  const [recipeCostManual, setRecipeCostManual] = useState(
    formData?.is_cost_manual || false
  );
  // Sync local state with prop changes
  useEffect(() => {
    // Handle both data.ingredients and data directly being an array
    const ingredientsDetails = Array.isArray(formData)
      ? formData
      : formData?.ingredients || [];
    // Transform API response to match expected format
    const transformedIngredients =
      ingredientsDetails?.map((ingredient) => {
        return {
          ...ingredient,
          category: ingredient?.categories || ingredient?.category,
        };
      }) || [];

    setLocalIngredients(transformedIngredients);
  }, [formData, formData?.ingredients]);

  useEffect(() => {
    fetchRecipeMeasures();
    fetchCookingMethods();
    fetchPreparationMethods();
  }, []);

  // Use local ingredientsData for immediate UI updates
  const ingredientsData = localIngredients;

  // API function to fetch ingredients based on search term
  const fetchIngredients = async (searchQuery = '') => {
    if (!searchQuery.trim()) {
      setIngredientDatabase([]);
      return;
    }

    try {
      setIsLoadingIngredients(true);
      const response = await getIngredientItemsList(
        searchQuery,
        '',
        { status: 'active' },
        '',
        ''
      );

      // Transform API response to match expected format
      const transformedIngredients =
        response?.ingredients?.map((ingredient) => ({
          ...ingredient,
          preparationMethod: null,
          cookingMethod: null,
          quantity: 1,
        })) || [];

      setIngredientDatabase(transformedIngredients);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch ingredients'
      );
      setIngredientDatabase([]);
    } finally {
      setIsLoadingIngredients(false);
    }
  };

  const fetchRecipeMeasures = async () => {
    try {
      const response = await getRecipeMeasuresList(
        '',
        '',
        '',
        'active',
        '',
        ''
      );

      if (response?.measures && response.measures.length > 0) {
        // Transform API data to match expected format
        const transformedMeasures = response?.measures?.map((measure) => ({
          label: measure?.unit_title,
          value: measure?.unit_slug,
          id: measure?.id,
        }));

        setUnitsOfMeasureOptions(transformedMeasures);
      } else {
        setUnitsOfMeasureOptions([]);
      }
    } catch (error) {
      setApiMessage('error', error?.response?.data?.message);
      setUnitsOfMeasureOptions([]);
    }
  };

  // API functions to fetch cooking and preparation methods using getAttributeList
  const fetchCookingMethods = async () => {
    try {
      const response = await getAttributeList(
        '',
        '',
        { status: 'active' },
        '',
        '',
        'ingredient_cooking_method' // type
      );
      // Transform API response to match expected format
      const transformedCookingMethods =
        response?.attributes?.map((method) => ({
          label: method?.attribute_title,
          value: method?.id,
        })) || [];
      setCookingMethods(transformedCookingMethods);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch cooking methods'
      );
      setCookingMethods([]);
    }
  };

  const fetchPreparationMethods = async () => {
    try {
      const response = await getAttributeList(
        '',
        '',
        { status: 'active' },
        '',
        '',
        'preparation_method' // type
      );
      // Transform API response to match expected format
      const transformedPreparationMethods =
        response?.attributes?.map((method) => ({
          label: method?.attribute_title,
          value: method?.id,
        })) || [];
      setPreparationMethods(transformedPreparationMethods);
    } catch (error) {
      setApiMessage(
        'error',
        error?.response?.data?.message || 'Failed to fetch preparation methods'
      );
      setPreparationMethods([]);
    }
  };

  // Debounced search effect
  useEffect(() => {
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    const timeout = setTimeout(() => {
      if (searchTerm?.trim()) {
        fetchIngredients(searchTerm);
      } else {
        setIngredientDatabase([]);
      }
    }, 300); // 300ms debounce

    setSearchTimeout(timeout);

    return () => {
      if (timeout) {
        clearTimeout(timeout);
      }
    };
  }, [searchTerm]);

  // Use ingredientDatabase directly since it's already filtered by API
  const filteredIngredients = ingredientDatabase || [];

  const addIngredient = (ingredientData = null) => {
    const newIngredient = {
      ...ingredientData,
      cost_per_unit: ingredientData?.cost,
      updatedPerUnitCost: ingredientData?.cost,
      measure_title: ingredientData?.measure_of_cost?.unit_title || 'g',
    };
    const updatedIngredients = [newIngredient, ...(ingredientsData || [])];

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    const allergenIds = updatedIngredients?.flatMap((item) => {
      if (item?.allergen_attributes) {
        return item?.allergen_attributes.map((attr) => attr?.id);
      } else if (item?.allergy) {
        return item?.allergy.map((attr) => attr?.id);
      }
      return [];
    });

    // Ensure dispatch is called properly
    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });

      if (allergenIds?.length > 0) {
        dispatch({
          type: 'UPDATE_NUTRITION',
          payload: { commonAllergens: allergenIds },
        });
      }
    }

    setSearchTerm('');
    setShowSuggestions(false);
  };

  const updateIngredient = async (index, field, value) => {
    if (!ingredientsData?.[index]) return;

    const updatedIngredients = [...(ingredientsData || [])];
    const ingredient = { ...updatedIngredients[index] };

    if (field === 'unit') {
      // Get the original per-unit cost and its original unit
      const originalUnit = ingredient?.measure_title || 'g';
      const perUnitCost = ingredient?.updatedPerUnitCost ?? 0;

      // Convert per-unit cost to the new unit
      const newPerUnitCost = convertCost(perUnitCost, originalUnit, value);

      // Update unit fields
      ingredient.measure_of_cost = {
        // ...(ingredient.measure_of_cost || {}),
        unit_title: value,
        id:
          unitsOfMeasureOptions?.find((item) => item?.value === value)?.id ||
          value,
      };
      ingredient.cost = newPerUnitCost;
      // ingredient.cost_per_unit = newPerUnitCost;
    } else {
      ingredient[field] = value;
    }

    updatedIngredients[index] = ingredient;

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    // Always update the ingredient
    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }
  };

  const removeIngredient = (index) => {
    const updatedIngredients =
      ingredientsData?.filter?.((_, i) => i !== index) || [];

    // Update local state immediately for UI responsiveness
    setLocalIngredients(updatedIngredients);

    if (dispatch) {
      dispatch({ type: 'UPDATE_INGREDIENTS', payload: updatedIngredients });
    }
  };

  const selectIngredientFromDatabase = (ingredientData) => {
    if (ingredientData) {
      addIngredient(ingredientData);
    }
  };

  const addFirstIngredient = () => {
    router.push('/recipes/ingredients/create');
  };

  return (
    <div className="ingredients-section">
      {/* Add Ingredient Search */}
      <div className="ingredients-section__search">
        <div className="ingredients-section__header">
          <h3 className="ingredients-section__title">Recipe Ingredients</h3>
        </div>

        <div className="ingredients-section__search-input">
          <CustomTextField
            value={searchTerm}
            onChange={(e) => {
              const value = e?.target?.value || '';
              setSearchTerm(value);
              setShowSuggestions(value.length > 0);
            }}
            onFocus={() => setShowSuggestions((searchTerm?.length || 0) > 0)}
            onBlur={() => {
              // Delay hiding suggestions to allow for clicks
              setTimeout(() => setShowSuggestions(false), 200);
            }}
            placeholder="Search ingredients database..."
            fullWidth
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <Icon
                    name="Search"
                    size={20}
                    className="ingredients-section__search-icon"
                  />
                </InputAdornment>
              ),
              endAdornment: isLoadingIngredients && (
                <InputAdornment position="end">
                  <Icon
                    name="Loader"
                    size={16}
                    className="ingredients-section__loading-icon"
                  />
                </InputAdornment>
              ),
            }}
          />
        </div>

        {/* Search Suggestions */}
        {showSuggestions && searchTerm?.trim() && (
          <div className="ingredients-section__suggestions">
            {isLoadingIngredients ? (
              <div className="ingredients-section__suggestion-loading">
                <Icon name="Loader" size={16} />
                <span>Searching ingredients...</span>
              </div>
            ) : (filteredIngredients?.length || 0) > 0 ? (
              filteredIngredients?.slice?.(0, 10)?.map?.((ingredient) => (
                <button
                  key={ingredient?.id}
                  onClick={() => selectIngredientFromDatabase(ingredient)}
                  className="ingredients-section__suggestion-item"
                >
                  <div className="ingredients-section__suggestion-content">
                    <div className="ingredients-section__suggestion-icon">
                      <Icon name="Package" size={14} />
                    </div>
                    <div className="ingredients-section__suggestion-details">
                      <div className="ingredients-section__suggestion-name">
                        {ingredient?.ingredient_name || 'Unknown Ingredient'}
                      </div>
                      <div className="ingredients-section__suggestion-meta">
                        {ingredient?.category &&
                        ingredient?.category?.length > 0
                          ? ingredient?.category
                              ?.map((cat) => cat?.category_name)
                              .join(', ') + ' • '
                          : ''}
                        {currency}
                        {ingredient?.cost?.toFixed?.(2) || '0.00'}/
                        {ingredient?.measure_of_cost?.unit_title || ''}
                      </div>
                      {ingredient?.ingredient_description && (
                        <div className="ingredients-section__suggestion-description">
                          {ingredient.ingredient_description}
                        </div>
                      )}
                    </div>
                  </div>
                  <Icon name="Plus" size={16} />
                </button>
              )) || []
            ) : (
              <div className="ingredients-section__suggestion-empty">
                <Icon name="Search" size={16} />
                <span>No ingredients found for "{searchTerm}"</span>
              </div>
            )}
          </div>
        )}
      </div>
      <div>
        <div>
          <CustomCheckbox
            checked={isCookingMethod}
            onChange={() => {
              setIsCookingMethod(!isCookingMethod);
              formData.isCookingMethod = !isCookingMethod;
            }}
            label="Display cooking method"
          />
        </div>
        <div>
          <CustomCheckbox
            checked={isPreparationMethod}
            onChange={() => {
              setIsPreparationMethod(!isPreparationMethod);
              formData.isPreparationMethod = !isPreparationMethod;
            }}
            label="Display preparation method"
          />
        </div>
        <div>
          <CustomCheckbox
            checked={recipeCostManual}
            onChange={() => {
              setRecipeCostManual(!recipeCostManual);
              formData.is_cost_manual = !recipeCostManual;
            }}
            label="Enter recipe cost manually"
          />
        </div>
      </div>
      {/* Ingredients List */}
      <div className="ingredients-section__list">
        {(ingredientsData?.length || 0) === 0 ? (
          <div className="ingredients-section__empty-state">
            <Icon
              name="Package"
              size={48}
              className="ingredients-section__empty-icon"
            />
            <h4 className="ingredients-section__empty-title">
              No ingredients added yet
            </h4>
            <p className="ingredients-section__empty-description">
              Search for ingredients above or add custom ingredients to get
              started
            </p>
            <CustomButton
              onClick={() => addFirstIngredient()}
              variant="contained"
            >
              Add First Ingredient
            </CustomButton>
          </div>
        ) : (
          ingredientsData?.map?.((ingredient, index) => (
            <div
              key={ingredient?.id || index}
              className="ingredients-section__item"
            >
              {/* Ingredient Header */}
              <div className="ingredients-section__item-header">
                <div className="ingredients-section__item-info">
                  <div className="ingredients-section__item-icon">
                    <Icon name="Package" size={16} color="#D97706" />
                  </div>
                  <div className="ingredients-section__item-details">
                    <input
                      type="text"
                      value={ingredient?.ingredient_name || ''}
                      onChange={(e) =>
                        updateIngredient(index, 'name', e?.target?.value || '')
                      }
                      placeholder="Ingredient name"
                      className="ingredients-section__item-name"
                    />
                    {ingredient?.category &&
                    ingredient?.category?.length > 0 ? (
                      <div className="ingredients-section__item-category">
                        {ingredient?.category
                          ?.map((cat) => cat?.category_name)
                          .join(', ')}
                      </div>
                    ) : (
                      '-'
                    )}
                  </div>
                </div>

                <button
                  onClick={() => removeIngredient(index)}
                  className="ingredients-section__remove-button"
                  aria-label={`Remove ${ingredient?.ingredient_name || 'ingredient'}`}
                >
                  <Icon name="Trash2" size={16} color="currentColor" />
                </button>
              </div>

              {/* Quantity and Unit */}
              <div className="ingredients-section__grid-2">
                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Quantity"
                    type="number"
                    value={ingredient?.quantity || ''}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'quantity',
                        parseFloat(e?.target?.value) || 0
                      )
                    }
                    inputProps={{ step: 1, min: 0 }}
                    fullWidth
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomSelect
                    label="Unit"
                    name={`unit`}
                    fullWidth
                    options={unitsOfMeasureOptions}
                    value={
                      unitsOfMeasureOptions?.find((item) => {
                        return (
                          item?.value ===
                          ingredient?.measure_of_cost?.unit_title
                        );
                      }) || ''
                    }
                    onChange={(e) =>
                      updateIngredient(index, 'unit', e?.value || 'g')
                    }
                    isClearable={false}
                    menuPosition="fixed"
                  />
                </div>
              </div>

              {/* Cost and Wastage */}
              <div className="ingredients-section__grid-3">
                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label={`Unit Cost (Per Unit Cost: ${parseFloat(
                      ingredient?.cost?.toFixed(2)
                    )})`}
                    type="number"
                    value={
                      parseFloat(
                        (ingredient?.cost * ingredient?.quantity)?.toFixed(2)
                      ) || ''
                    }
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'cost',
                        parseFloat(e?.target?.value) || 0
                      )
                    }
                    inputProps={{ step: 1, min: 0 }}
                    fullWidth
                    disabled
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {currency}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Wastage"
                    type="number"
                    name="waste_percentage"
                    value={ingredient?.waste_percentage || 0}
                    onChange={(e) =>
                      updateIngredient(
                        index,
                        'waste_percentage',
                        parseInt(e?.target?.value) || 0
                      )
                    }
                    fullWidth
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">%</InputAdornment>
                      ),
                    }}
                  />
                </div>

                <div className="ingredients-section__field-group">
                  <CustomTextField
                    label="Final Cost"
                    value={
                      parseFloat(
                        (
                          ingredient?.cost *
                          ingredient?.quantity *
                          (1 + (ingredient.waste_percentage || 0) / 100)
                        )?.toFixed(2)
                      ) || ''
                    }
                    fullWidth
                    disabled
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          {currency}
                        </InputAdornment>
                      ),
                    }}
                  />
                </div>
              </div>

              {/* Preparation Methods */}
              <div className="ingredients-section__grid-2">
                {isCookingMethod && (
                  <div className="ingredients-section__field-group">
                    <CustomSelect
                      label="Cooking Method"
                      name={`cookingMethod-${index}`}
                      fullWidth
                      options={cookingMethods}
                      value={
                        cookingMethods?.find((item) => {
                          return item?.value === ingredient?.cookingMethod;
                        }) || ''
                      }
                      onChange={(e) =>
                        updateIngredient(
                          index,
                          'cookingMethod',
                          e?.value || null
                        )
                      }
                      isClearable={false}
                    />
                  </div>
                )}

                {isPreparationMethod && (
                  <div className="ingredients-section__field-group">
                    <CustomSelect
                      label="Preparation"
                      name={`preparationMethod-${index}`}
                      fullWidth
                      options={preparationMethods}
                      value={
                        preparationMethods?.find((item) => {
                          return item?.value === ingredient?.preparationMethod;
                        }) || ''
                      }
                      onChange={(e) =>
                        updateIngredient(
                          index,
                          'preparationMethod',
                          e?.value || null
                        )
                      }
                      isClearable={false}
                    />
                  </div>
                )}
              </div>

              {/* Allergens Display */}
              {ingredient?.allergy &&
                (ingredient?.allergy?.length || 0) > 0 && (
                  <div className="ingredients-section__allergens">
                    <Icon name="AlertTriangle" size={14} color="#F59E0B" />
                    <span className="ingredients-section__allergens-text">
                      Contains:{' '}
                      {ingredient?.allergy
                        ?.map((att) => att?.attribute_title)
                        .join(', ')}
                    </span>
                  </div>
                )}
            </div>
          ))
        )}
      </div>

      {/* Ingredients Summary */}
      {(ingredientsData?.length || 0) > 0 && (
        <div className="ingredients-section__summary">
          <h4 className="ingredients-section__summary-title">
            Ingredients Summary
          </h4>
          <div className="ingredients-section__summary-grid">
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {ingredientsData?.length || 0}
              </div>
              <div className="ingredients-section__summary-label">
                Total Ingredients
              </div>
            </div>
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {currency}
                {ingredientsData
                  ?.reduce((sum, ingredient) => {
                    const cost = ingredient?.cost || 0;
                    const quantity = ingredient?.quantity || 0;
                    const wasteMultiplier =
                      1 + (ingredient?.waste_percentage || 0) / 100;
                    const finalCost = cost * quantity * wasteMultiplier;
                    return sum + finalCost;
                  }, 0)
                  ?.toFixed(2) || '0.00'}
              </div>
              <div className="ingredients-section__summary-label">
                Total Cost
              </div>
            </div>
            <div className="ingredients-section__summary-item">
              <div className="ingredients-section__summary-value">
                {[
                  ...new Set(
                    ingredientsData?.flatMap?.((ing) => ing?.allergy || []) ||
                      []
                  ),
                ]?.length || 0}
              </div>
              <div className="ingredients-section__summary-label">
                Unique Allergens
              </div>
            </div>
          </div>
        </div>
      )}

      {validationErrors?.ingredients && (
        <div className="ingredients-section__error">
          <Icon name="AlertCircle" size={16} />
          <span className="other-field-error-text">
            {validationErrors.ingredients}
          </span>
        </div>
      )}
    </div>
  );
};

export default IngredientsSection;
