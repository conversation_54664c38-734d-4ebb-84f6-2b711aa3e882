import axiosInstance from '@/helper/axios/axiosInstance';
import { SUPPORT_TICKET_URLS } from '@/helper/constants/urls';

export const supportTicketService = {
  // Get support ticket dashboard data
  getDashboardData: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        SUPPORT_TICKET_URLS?.SUPPORT_TICKET_DASHBOARD
      );
      if (status === 200) {
        return data?.data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },
};
