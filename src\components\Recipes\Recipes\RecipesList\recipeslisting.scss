.recipes-listing {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  scroll-behavior: smooth;
  // background-color: var(--color-secondary);

  &__main {
    padding: var(--spacing-lg) var(--spacing-xl);
  }
  &__main--public {
    padding: var(--spacing-lg) var(--spacing-none) var(--spacing-none);
  }
  // &__container {
  // max-width: 1280px;
  // margin: 0 auto;
  // padding: 0 var(--spacing-lg);
  // padding-top: var(--spacing-2xl);
  // padding-bottom: var(--spacing-2xl);

  // @media (max-width: 768px) {
  //   padding: 0 var(--spacing-sm);
  //   padding-top: var(--spacing-lg);
  //   padding-bottom: var(--spacing-lg);
  // }

  // @media (max-width: 1024px) {
  //   padding: 0 var(--spacing-lg);
  //   padding-top: var(--spacing-xl);
  //   padding-bottom: var(--spacing-xl);
  // }
  // }

  // Header Section
  &__header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xsm) var(--spacing-md);
  }
  &__header--public {
    padding: var(--spacing-lg);
  }
  &__header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
  }

  // Search Section
  &__search-section {
    width: 100%;
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
  }

  // Controls Section
  &__controls {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 0;
    margin-bottom: var(--spacing-lg);
    @media (max-width: 1200px) {
      flex-direction: column;
      gap: var(--spacing-lg);
    }
  }

  &__controls-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    width: 100%;
    flex-wrap: wrap;
  }

  &__controls-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    width: 40%;
    justify-content: flex-end;
    @media (max-width: 1200px) {
      width: 100%;
      justify-content: flex-start;
    }
  }

  // Sort Section
  &__sort-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }

  &__sort-icon {
    color: var(--text-color-slate-gray);
  }

  &__sort-select {
    border: 1px solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    background-color: var(--color-white);
    color: var(--text-color-primary);

    &:focus {
      outline: none;
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-opacity);
    }
  }

  // View Toggle
  &__view-toggle {
    display: flex;
    align-items: center;
    background-color: var(--color-white);
    border: 1px solid var(--border-color-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-xxs);
  }

  &__view-btn {
    padding: var(--spacing-xs);
    border-radius: var(--border-radius-sm);
    background: transparent;
    border: none;
    color: var(--text-color-slate-gray);
    cursor: pointer;

    &:hover {
      color: var(--color-primary);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--color-white);
    }
  }

  // Results Count
  &__results-count {
    font-size: var(--font-size-sm);
    color: var(--text-color-slate-gray);
    font-family: var(--font-family-primary);
  }

  // Filter Button
  &__filter-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--btn-padding-sm);
    border-radius: var(--btn-icon-border-radius);
    border: var(--normal-sec-border);
    background-color: var(--color-white);
    color: var(--text-color-slate-gray);
    font-size: var(--font-size-sm);
    font-family: var(--font-family-primary);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      background-color: var(--color-primary-opacity);
      color: var(--color-primary);
      border-color: var(--color-primary);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    &--active {
      background-color: var(--color-primary);
      color: var(--color-white);
      border-color: var(--color-primary);
      transform: translateY(-1px);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.15);
    }
  }

  // Content Section
  &__content {
    display: flex;
    gap: var(--spacing-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  &__grid-container {
    flex: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // Infinite Scroll Styles
  &__loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-color-secondary);
    font-size: var(--font-size-sm);
    animation: fadeInUp 0.3s ease;
    span {
      font-weight: 500;
      font-family: var(--font-family-primary);
    }
  }

  // &__loading-spinner {
  //   width: 20px;
  //   height: 20px;
  //   border: 2px solid var(--color-primary-opacity);
  //   border-top: 2px solid var(--color-primary);
  //   border-radius: 50%;
  //   animation: spin 1s linear infinite;
  // }

  // &__end-message {
  //   text-align: center;
  //   padding: var(--spacing-xl);
  //   color: var(--text-color-secondary);
  //   font-size: var(--font-size-sm);
  //   animation: fadeInUp 0.3s ease;

  //   p {
  //     margin: 0;
  //     padding: var(--spacing-lg);
  //     background: var(--color-light-grayish-blue);
  //     border-radius: var(--border-radius-md);
  //     display: inline-block;
  //   }
  // }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &__loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--color-light-gray);
    border-top: 2px solid var(--color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  &__end-message {
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-color-secondary);
    font-size: var(--font-size-sm);

    p {
      margin: 0;
      font-weight: 500;
      font-family: var(--font-family-primary);
    }
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
}

.recipes-listing__categories {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  // background: var(--color-white);
  // padding: var(--spacing-lg) 0 var(--spacing-md) 0;
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);

  .recipes-listing__category-arrow {
    background: var(--color-white);
    border: var(--normal-sec-border);
    outline: none;
    cursor: pointer;
    position: absolute;
    top: 50%;
    z-index: 2;
    transform: translateY(-50%);
    padding: var(--spacing-xs);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    &.left {
      left: 5px;
    }
    &.right {
      right: 5px;
    }
    &:hover {
      background: var(--color-secondary-auth);
      transform: translateY(-50%) scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    svg {
      display: block;
      color: var(--icon-color-primary);
    }
  }

  .recipes-listing__category-chips {
    display: flex;
    gap: var(--spacing-lg);
    width: 100%;
    overflow-x: auto;
    padding: var(--spacing-xs) var(--spacing-none);
    scroll-behavior: smooth;
    scrollbar-width: none; // Firefox
    &::-webkit-scrollbar {
      display: none;
    } // Chrome/Safari
  }

  .recipes-listing__category-chip {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: transparent;
    border: var(--normal-sec-border);
    outline: none;
    cursor: pointer;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    justify-content: center;
    min-width: 110px;
    span {
      margin-top: var(--spacing-xs);
      font-size: var(--font-size-xs);
      color: var(--text-color-black);
      font-family: var(--font-family-primary);
      font-weight: var(--font-weight-regular);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .recipes-listing__category-chip-svg,
    .recipes-listing__category-chip-icon {
      width: 30px;
      height: 30px;
      display: block;
      margin: 0 auto;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: var(--icon-color-black);
      filter: grayscale(100%);
    }

    &--active {
      background: var(--color-primary-opacity);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      span {
        color: var(--text-color-primary);
        font-weight: var(--font-weight-semibold);
      }
      .recipes-listing__category-chip-svg,
      .recipes-listing__category-chip-icon {
        // color: var(--icon-color-primary);
        filter: none;
        transform: scale(1.1);
      }
    }

    &:hover:not(&--active) {
      background: var(--color-secondary);
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      span {
        color: var(--color-primary);
        font-weight: var(--font-weight-semibold);
      }
      .recipes-listing__category-chip-svg,
      .recipes-listing__category-chip-icon {
        // color: var(--color-primary);
        filter: none;
        transform: scale(1.05);
      }
    }
  }

  // Skeleton loading styles
  .recipes-listing__category-loading {
    display: flex;
    gap: var(--spacing-lg);
    .recipes-listing__category-chip--loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      .skeleton-icon {
        width: 30px;
        height: 30px;
        background: var(--color-light-gray);
        border-radius: 50%;
        margin-bottom: var(--spacing-xs);
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
      .skeleton-text {
        width: 40px;
        height: 10px;
        background: var(--color-light-gray);
        border-radius: var(--border-radius-xs);
        background: linear-gradient(
          90deg,
          #f0f0f0 25%,
          #e0e0e0 50%,
          #f0f0f0 75%
        );
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
      }
    }
  }
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
